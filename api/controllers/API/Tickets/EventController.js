'use strict';

const argv              = require('optimist').argv;
const stripeSettingsRow = argv.prod?'stripe_connect':'stripe_connect_dev';
const plaidSettignsRow  = argv.prod?'plaid_prod':'plaid_dev';
const swUtils = require('../../../lib/swUtils');
const tilledConfig = sails.config.tilled;

const { FEE_PAYER } = require('../../../constants/payments');
const TilledService = require('../../../services/TilledService');

const FILE_PATH = 'api/controllers/API/Tickets/EventController.js';

module.exports = {
    // get /api/tickets/events
    eventsList: function (req, res) {
        let query = 
            `SELECT    
                e.event_tickets_code "event_id", 
                e.name, e.long_name, e.tickets_options,   
                TO_CHAR(e.date_start, 'MM/DD') "date_start",   
                TO_CHAR(e.date_end, 'MM/DD') "date_end",   
                e.city, e.state,
                COALESCE((e.tickets_settings ->> 'use_merchandise_sales')::BOOLEAN, false)  "use_merchandise_sales", 
                COALESCE((tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
                STRING_AGG(
                    FORMAT('%s: %s', et.label, TO_CHAR(et.current_price::NUMERIC, '$FM999G999.0099')), ', '
                ) "tickets", (
                    CASE 
                        WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                        ELSE 'tickets'
                    END
                ) "sales_type", (
                    SELECT ROW_TO_JSON(opts)
                    FROM (
                        SELECT 
                            e.tickets_purchase_by_card "card", 
                            e.tickets_purchase_by_ach "ach"
                    ) "opts"
                ) "payment_option"
            FROM "event" e  
            INNER JOIN event_ticket et  
                ON et.event_id = e.event_id  
                AND et.current_price IS NOT NULL 
            WHERE e.allow_ticket_sales IS TRUE  
                AND e.tickets_published IS TRUE  
                AND e.event_tickets_code IS NOT NULL 
                AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end  
                AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start  
                AND e.deleted IS NULL
                AND e.live_to_public IS TRUE
                AND e.ticket_camps_registration IS NOT TRUE
                AND e.show_on_home_page IS TRUE
            GROUP BY e.event_id 
            ORDER BY e.name`;

        Cache.getResult(
            req.path,
            () => Db.query(query),
            {
                ttl: Cache.TTL_SWT,
                tags: [
                    Cache.tag.dbTable('event'),
                    Cache.tag.dbTable('event_ticket'),
                ],
                req,
            }
        ).then(result => {
            res.status(200).json({ events: result.rows });
        }).catch(res.customRespError.bind(res))
    },

    // get /api/kiosk/events/:event
    kioskTickets: function (req, res) {
        let $eventTicketsCode   = parseInt(req.params.event, 10),
            $discount           = req.query.discount;

        if(!$eventTicketsCode) {
            return res.validation('Invalid Event Identifier specified');
        }

        // generate "payment" hash and return X-hash header to client
        SWTWebpointRequestLogService.XHashHeader(res, $eventTicketsCode);
        getEventTickets(req, $eventTicketsCode, $discount, true)
            .then(function (data) {
                res.status(200).json(data);
            }, function (err) {
                res.customRespError(err);
            })
    },

    // GET /api/tickets/events/:event/coupons
    getAllowedTicketsToBuy: (req, res) => {
        let eventID = Number(req.params.event);
        let ticketCoupons = req.query.coupons;

        if(!_.isNumber(eventID) || _.isNaN(eventID)) {
            return res.validation('Invalid Event Identifier specified');
        }

        if(!Array.isArray(ticketCoupons) || _.isEmpty(ticketCoupons)) {
            return res.validation('Invalid Coupons specified');
        }

        return CouponService.purchase.getCouponsTickets(eventID, ticketCoupons)
            .then(response => res.status(200).json({ tickets: response }))
            .catch(res.customRespError.bind(res));
    },

    // get /api/tickets/events/:event
    eventTickets: function (req, res, next) {
        let $eventTicketsCode   = parseInt(req.params.event, 10),
            $discount           = req.query.discount;

        if(!$eventTicketsCode) {
            return res.validation('Invalid Event Identifier specified');
        }

        // generate "payment" hash and return X-hash header to client
        SWTWebpointRequestLogService.XHashHeader(res, $eventTicketsCode);

        getEventTickets(req, $eventTicketsCode, $discount, false)
            .then(function (data) {
                res.status(200).json(data);
            }, function (err) {
                res.customRespError(err);
            })
    },
    // get /api/tickets/events/:event/types
    reloadTicketTypes: function (req, res) {
        let $eventCode  = parseInt(req.params.event, 10),
            $discount   = req.query.discount;

        if(!$eventCode) return res.validation('Invalid Event Identifier specified');

        Db.query(
            `SELECT e.event_id, (
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                    ELSE 'tickets'
                END
             ) "sales_type",
             COALESCE(e.tickets_sw_fee, 0) "sw_fee"
             FROM "event" e 
             WHERE e.event_tickets_code = $1
                AND (e.allow_ticket_sales IS TRUE OR e.ticket_camps_registration IS TRUE) 
                AND (e.tickets_published IS TRUE OR e.tickets_visible IS TRUE) `,
            [$eventCode]
        ).then(result => {
            let eventRow = result.rows[0];

            __validateEventRow(eventRow);

            return eventRow;
        }).then(eventRow => {
            return __findTicketTypes(req, eventRow.event_id, eventRow.sales_type, eventRow.sw_fee, { discount: $discount }, null, false);
        }).then(tickets => {
            res.status(200).json({ tickets });
        }).catch(res.customRespError.bind(res));
    },
    // get /api/tickets/events/:event/invoice/:invoice
    loadInvoice: function (req, res) {
        let $eventCode      = parseInt(req.params.event, 10),
            $invoiceCode    = req.params.invoice;

       getPurchaseData(req, $invoiceCode, $eventCode, res, true);
    },
    // get /api/tickets/events/:event/invoice/:invoice/type-change
    loadPaymentForTypeChange: function (req, res) {
        let eventCode   = Number(req.params.event);
        let invoice     = req.params.invoice;

        getPurchaseData(req, invoice, eventCode, res, false);
    }
}

function getPurchaseData (req, barcode, eventCode, res, isInvoice) {
    if(!eventCode) return res.validation('Invalid Event Identifier specified');
    if(!barcode) return res.validation('Empty Invoice Identifier passed');

    let ticketBarcode;

    try {
        ticketBarcode = SWTReceiptService.convertHashToBarcode(barcode);
    } catch (e) {
        return res.validation(e.message);
    }

    Promise.all([
        findEventRow(req, eventCode),
        isInvoice 
            ? buyTicketsService.paymentFinder.getInvoiceData(eventCode, ticketBarcode) 
            : buyTicketsService.paymentFinder.getPurchaseForTypeChange(eventCode, ticketBarcode) 
    ]).then(results => {
        let event       = results[0],
            payment     = results[1];

        if(_.isEmpty(payment)) {
            throw { validation: 'Suitable payment not found or the invoice was canceled' }
        }
        // check if payment has no items.
        payment.amount = +payment.amount;

        if (!isInvoice) {
            event.payment_option = {
                card: true
            }
        }

        res.status(200).json({ event, payment });

    }).catch(res.customRespError.bind(res));
}

function findEventRow (req, eventCode) {
    return Cache.getResult(`${FILE_PATH}:findEventRow:${JSON.stringify({eventCode})}`,() => Db.query(
        `SELECT 
             e.event_id,
             COALESCE(e.event_kiosk_description, '[]')::JSON "event_kiosk_description",
             e.tickets_visible, (
                CASE 
                    WHEN e.tickets_purchase_by_card IS TRUE
                        THEN (
                            CASE
                                WHEN e.tickets_use_connect IS TRUE 
                                    THEN (
                                        SELECT "value"->>'public_key' 
                                        FROM "settings" 
                                        WHERE "key" = '${stripeSettingsRow}'
                                    )
                                ELSE COALESCE(
                                        sa.public_key, 
                                        (SELECT "value"->>'public_key' 
                                         FROM "settings" 
                                         WHERE "key" = '${stripeSettingsRow}')
                                    )
                            END
                        )
                    ELSE ''
                END
             ) "stripe_key", (
                CASE 
                    WHEN e.tickets_purchase_by_ach IS TRUE 
                        THEN (
                            SELECT "value"->>'public_key' 
                            FROM "settings" 
                            WHERE "key" = '${plaidSettignsRow}'
                        )
                    ELSE ''
                END 
             ) "plaid_key", COALESCE(e."tickets_settings"->>'kiosk_surcharge', '0') "kiosk_surcharge",
             (
                SELECT COALESCE(JSONB_AGG(
                               JSONB_BUILD_OBJECT(
                                       'form_id', cfe.custom_form_event_id,
                                       'type', cfe.type
                               )
                       ), '[]'::jsonb)
                FROM custom_form_event cfe
                WHERE cfe.type IN ('camps_purchase_page')
                  AND cfe.published IS NOT NULL
                  AND cfe.event_id = e.event_id
             ) custom_forms_requires_submitting,
             COALESCE((e.tickets_settings ->> 'show_ncsa_athlete_form')::BOOLEAN, false) IS TRUE "show_ncsa_athlete_form",
             COALESCE((e.tickets_settings ->> 'no_junk_tax_prices')::BOOLEAN, false) IS TRUE "no_junk_tax_prices",
             TO_CHAR(e.date_start, 'YYYY-MM-DD') "date_start", TO_CHAR(e.date_end, 'YYYY-MM-DD') "date_end", 
             e.website, e.city, e.state, e.long_name "name", e.tickets_description "description", 
             COALESCE(              
                (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG("fields")) 
                    FROM JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) "fields"
                    WHERE ("fields"->'show_on'->>'purchase')::BOOLEAN IS TRUE 
                        OR ("fields"->'show_on'->>'purchase_start')::BOOLEAN IS TRUE
                ), '[]'::JSON
            ) "additional_fields", e.social_links,
            (
                SELECT eec.is_active
                      FROM event_ticket_buy_entry_code_settings eec
                      WHERE eec.event_id = e.event_id
             ) event_ticket_buy_entry_code_required,
             COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) IS TRUE "require_ticket_coupon",
             e.tilled_tickets_fee_payer "tilled_fee_payer", 
             e.tilled_tickets_account_id "tilled_account_id",
             COALESCE((e.tickets_payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider",
             e.stripe_tickets_fee_payer "stripe_fee_payer", e.tickets_sw_fee_payer "sw_fee_payer", (
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                    ELSE 'tickets'
                END
             ) "sales_type", 
             (
                CASE
                    WHEN (e.stripe_tickets_percent > 0)
                        THEN (e.stripe_tickets_percent / 100)
                    ELSE 0
                END
             )::REAL "stripe_percent",
             (
                CASE
                    WHEN (e.tilled_tickets_percentage > 0)
                        THEN (e.tilled_tickets_percentage / 100)
                    ELSE 0
                END
             )::REAL "tilled_percentage",
            (
                SELECT file_path || '.' || file_ext FROM "event_media" 
                WHERE "event_id" = e.event_id
                    AND "file_type" = 'small-logo'
             ) "small_logo",
             e.showcase_registration,
             COALESCE(e.stripe_tickets_fixed, 0)::REAL "stripe_fixed",
             COALESCE(e.tilled_tickets_fixed, 0)::REAL "tilled_fixed",
             e.tickets_waiver_agreement "waiver_agreement", 
             (COUNT(td.*) > 0) "has_discounts",
             COALESCE(e.tickets_sw_fee, 0) "sw_fee", (
                SELECT ROW_TO_JSON(opts)
                FROM (
                    SELECT 
                        e.tickets_purchase_by_card "card", 
                        e.tickets_purchase_by_check "check", 
                        e.tickets_purchase_by_ach "ach"
                ) "opts"
            ) "payment_option",
            e.tickets_check_payment_details "check_details",
            ((NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_start) "sales_not_started",
            ((NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_end) "sales_ended", (
                CASE 
                    WHEN (e."tickets_discount" IS NULL)
                        THEN FALSE 
                    WHEN (
                        SELECT COUNT(*) = 0
                        FROM (
                            SELECT JSONB_OBJECT_KEYS(e."tickets_discount"->'amount')
                        ) "d"
                    ) AND (
                        SELECT COUNT(*) = 0
                        FROM (
                            SELECT JSONB_OBJECT_KEYS(e."tickets_discount"->'quantity')
                        ) "d"
                    )
                        THEN FALSE
                    ELSE TRUE
                END
            ) "has_payment_discount",
            COALESCE(
                (e."tickets_settings"->>'max_allowed_tickets_in_purchase')::INT, 0
            ) "max_allowed_tickets_in_purchase",
            COALESCE((
                e.tickets_settings ->> 'use_vertical_insurance'
            )::BOOLEAN, false) "use_vertical_insurance",
            (e."tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE "require_tickets_names",
            (e."tickets_settings"->>'not_require_sw_fee_for_checks')::BOOLEAN IS TRUE "not_require_sw_fee_for_checks",
            COALESCE(e.tickets_locations, '[]'::JSON) event_locations,
            TO_CHAR(e.date_start, 'YYYY-MM-DD HH24:MI:SS') AS date_start_with_time,
            TO_CHAR(e.date_end, 'YYYY-MM-DD HH24:MI:SS') AS date_end_with_time,
            e.timezone
         FROM "event" e 
         LEFT JOIN "ticket_discount" td
             ON td.event_id = e.event_id
         LEFT JOIN "stripe_account" sa 
            ON sa.secret_key = e.stripe_tickets_private_key
         WHERE e.event_tickets_code = $1
            AND (e.allow_ticket_sales IS TRUE OR e.ticket_camps_registration IS TRUE) 
            AND (e.tickets_published IS TRUE OR e.tickets_visible IS TRUE) 
            AND e.deleted IS NULL
            AND e.live_to_public IS TRUE
         GROUP BY e.event_id, sa.public_key`,
        [eventCode]
    ), {
        ttl: Cache.TTL_SWT,
        tags: [
            Cache.tag.dbTable('event'),
            Cache.tag.dbTable('ticket_discount'),
            Cache.tag.dbTable('event_media'),
        ],
        req,
    }).then(result => result.rows[0])
}

function __validateEventRow (event) {
    if(_.isEmpty(event)) { 
        throw { 
            validation: 'Tournament not found.' 
        }
    }

    if(event.sales_not_started && !event.tickets_visible) {
        throw {
            validation: 'The Event Sales have NOT started yet.'
        }
    }

    if(event.sales_ended) {
        throw {
            validation : 'The Event Sales have ended already.',
            description: `#${event.event_id}`
        }
    }
}

function __findTicketTypes(req, eventId, type, swFee, params, kioskSurcharge, useKioskSurcharge) {
    return Cache.getResult(
        `${FILE_PATH}:__findTicketTypes:${JSON.stringify({
            eventId,
            type,
            swFee,
            params,
            kioskSurcharge,
            useKioskSurcharge
        })}`,
        async () => {
            let discountsJoin = '',
                discountsFields = '',
                ticketsSQL = '',
                ticektsSQLOrder = ` ORDER BY et.sort_order, et.event_ticket_id`,
                sql = '',
                SQLparams = [eventId, swFee],
                ticketsFilters  = [];

            if (params.discount) {
                discountsJoin =
                    `LEFT JOIN "ticket_discount" td 
                ON td.event_ticket_id = et.event_ticket_id 
                AND td.event_id = et.event_id 
                AND td.code = $3
                AND (td.max_count - td.used_count) > 0 
                AND td.discount > 0`;
                discountsFields =
                    `, (td.max_count - td.used_count) "available_discounts",
                td.discount::numeric "discount_amount"`;
                SQLparams.push(params.discount)
            }

            if(useKioskSurcharge) {
                ticketsFilters.push('et.visible_in_kiosk IS TRUE');
            }

            ticketsSQL =
                `SELECT   
                et.event_ticket_id,  
                et.label,  
                et.short_label,  
                et.initial_price,  
                et.merchandise_type,
                COALESCE(et.current_price, et.initial_price) "price",  
                COALESCE(NULLIF(et.application_fee, 0), ($2)::NUMERIC) "app_fee",
                et.current_price,  
                et.description, et.max_count,  
                et.ticket_type,
                et.sub_type,
                et.kiosk_surcharge,
                et.is_free,
                (SELECT ARRAY_AGG(TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'YYYY Dy, Mon DD'))
                 FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) "valid_dates",
                et.waitlisted, (
                    CASE 
                        WHEN (
                                et.waitlisted IS NOT TRUE AND 
                                et.waitlist_switching_count IS NOT NULL AND 
                                et.waitlist_switching_count > 0
                            )
                            THEN (  
                                (
                                    SELECT COUNT(p.purchase_id)
                                    FROM "purchase" p 
                                    INNER JOIN "purchase_ticket" pt 
                                        ON pt.purchase_id = p.purchase_id
                                    WHERE pt.event_ticket_id = et.event_ticket_id 
                                        AND p.event_id = et.event_id 
                                        AND p.status IS NOT NULL AND p.status <> 'canceled'
                                        AND p.type IN('card', 'check', 'free')
                                        AND pt.quantity > 0
                                        AND p.canceled_date IS NULL
                                ) >= et.waitlist_switching_count
                            )
                        ELSE FALSE
                    END 
                ) "waitlist_limit_exceeded",
                (et."has_barcode" IS NOT TRUE) "is_donation"
                ${discountsFields} 
            FROM event_ticket et  
            ${discountsJoin} 
            WHERE et.event_id = $1
                AND et.published IS TRUE
                ${ticketsFilters.length?'AND '+ticketsFilters.join(' AND '):''}`;

            if (type === 'camps') {
                /**
                 * Formula for "age_date" is taken from "v_swt_participant_age"
                 */
                sql =
                    `SELECT * FROM (
                SELECT ec.name "camp_name", ec.event_camp_id "id", ec.types_order, ec.age_from, ec.age_to,
                       TO_CHAR(ec.date_start, 'YYYY-MM-DD') "date_start", 
                       TO_CHAR(ec.date_end, 'YYYY-MM-DD') "date_end",
                       ec.dependency, ec.description, 
                       NOW() AT TIME ZONE e.timezone > ec.date_end "is_camp_finished",
                       TO_CHAR(COALESCE(ec.age_date, TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY')), 'YYYY-MM-DD') "age_date", (
                        SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t)))
                        FROM (
                           ${ticketsSQL}
                           AND et.event_camp_id = ec.event_camp_id
                           ${ticektsSQLOrder}
                        ) "t"
                    ) "types"
                 FROM "event_camp" ec
                 JOIN "event" e 
                    ON e."event_id" = ec."event_id"
                 WHERE ec.event_id = $1
                    AND ec."deleted" IS NULL
                    AND ec."visibility" = 'published'
                 ORDER BY sort_order
             ) "camps"
            WHERE JSON_ARRAY_LENGTH("camps".types) > 0`
            } else {
                sql = ticketsSQL + ticektsSQLOrder;
            }

            let {rows: ticketTypes} = await Db.query(sql, SQLparams);
            parseTicketsPrices(ticketTypes, kioskSurcharge, useKioskSurcharge);
            return ticketTypes;
        },
        {
            ttl: Cache.TTL_SWT,
            tags: [
                Cache.tag.dbTable('event_ticket'),
                Cache.tag.dbTable('ticket_discount'),
            ],
            req
        }
    );
}

function parseTicketsPrices (ticketTypes, kioskSurcharge, useKioskSurcharge) {
    ticketTypes.forEach(tt => {
        if(tt.camp_name && tt.id) {
            parseTicketsPrices(tt.types, kioskSurcharge, useKioskSurcharge);
        } else {
            let _kioskSurcharge = null;

            kioskSurcharge = Number(kioskSurcharge) || 0;

            if (useKioskSurcharge) {
                _kioskSurcharge = tt.kiosk_surcharge === null
                    ? kioskSurcharge
                    : Number(tt.kiosk_surcharge);
            } else {
                _kioskSurcharge = kioskSurcharge;
            }

            tt.price                    = swUtils.normalizeNumber(Number(tt.price) + _kioskSurcharge);
            tt.initial_price            = Number(tt.initial_price);
            tt.app_fee                  = Number(tt.app_fee);
            tt.current_price            = Number(tt.current_price);

            if(!tt.waitlisted && tt.waitlist_limit_exceeded) {
                tt.waitlisted = true;
                setWatilisted(tt.event_ticket_id);
            }

            tt.waitlist_limit_exceeded  = undefined;
            tt.kiosk_surcharge          = undefined
        }
    })
}

function getEventTickets (req, $eventTicketsCode, $discount, isForKiosk) {
    return findEventRow(req, $eventTicketsCode)
        .then(function (event) {
            __validateEventRow(event);

            let kioskSurcharge  = isForKiosk && Number(event.kiosk_surcharge) || 0;
            let small_logo      = event.small_logo ? sails.config.urls.home_page.baseUrl + event.small_logo : null;

            return __findTicketTypes(
                req, event.event_id, event.sales_type, event.sw_fee, { discount: $discount }, kioskSurcharge, isForKiosk
            ).then(function (rows) {
                let availableTickets = TicketsService.filterExpiredTickets(event.timezone, rows);

                return {
                    event: {
                        event_kiosk_description :  event.event_kiosk_description
                                                    && event.event_kiosk_description.join('\n'),
                        stripe_key              :  event.stripe_key,
                        plaid_key               :  event.plaid_key,
                        date_start              :  event.date_start,
                        date_end                :  event.date_end,
                        website                 :  event.website,
                        city                    :  event.city,
                        state                   :  event.state,
                        name                    :  event.name,
                        description             :  event.description,
                        additional_fields       :  event.additional_fields,
                        social_links            :  event.social_links,
                        sales_type              :  event.sales_type,
                        waiver_agreement        :  event.waiver_agreement,
                        has_discounts           :  event.has_discounts,
                        count_service_fee       :  isServiceFeeCounted(event),
                        stripe_percent          :  event.stripe_percent,
                        stripe_fixed            :  event.stripe_fixed,
                        stripe_fee_payer        :  event.stripe_fee_payer,
                        sw_fee_payer            :  event.sw_fee_payer,

                        tilled_public_key       : tilledConfig.publicKey,
                        tilled_account_id       : event.tilled_account_id,
                        tilled_is_sandbox       : tilledConfig.isSandbox,
                        tilled_fee_payer        : event.tilled_fee_payer,
                        tilled_fixed            : event.tilled_fixed || TilledService.__DEFAULT_FIXED_CARD_FEE__,
                        tilled_percentage       : event.tilled_percentage || TilledService.__DEFAULT_FIXED_CARD_PERCENTAGE__,
                        payment_provider        : event.payment_provider,

                        use_vertical_insurance  :  event.use_vertical_insurance,
                        payment_option          :  event.payment_option,
                        check_details           :  event.check_details,
                        has_payment_discount    :  event.has_payment_discount,
                        require_tickets_names   : event.require_tickets_names,
                        not_require_sw_fee_for_checks: event.not_require_sw_fee_for_checks,
                        event_id                : event.event_id,
                        event_locations         : event.event_locations,
                        small_logo              : small_logo,
                        date_start_with_time    : event.date_start_with_time,
                        date_end_with_time      : event.date_end_with_time,
                        require_ticket_coupon   : event.require_ticket_coupon,
                        show_ncsa_athlete_form  : event.show_ncsa_athlete_form,
                        no_junk_tax_prices      : event.no_junk_tax_prices,
                        no_available_tickets_by_date: !availableTickets.length,
                        showcase_registration   : event.showcase_registration,
                        event_ticket_buy_entry_code_required: event.event_ticket_buy_entry_code_required,
                        max_allowed_tickets_in_purchase: event.max_allowed_tickets_in_purchase,
                        custom_forms_requires_submitting: event.custom_forms_requires_submitting,
                    },
                    tickets: availableTickets
                }
            })
        })
}

function isServiceFeeCounted(event) {
    const {payment_provider, sw_fee_payer, stripe_fee_payer, tilled_fee_payer} = event

    if(sw_fee_payer) {
        return true
    }

    if(payment_provider === PaymentService.__PAYMENT_PROVIDERS__.STRIPE) {
        return stripe_fee_payer === FEE_PAYER.BUYER
    }

    if(payment_provider === PaymentService.__PAYMENT_PROVIDERS__.TILLED) {
        return tilled_fee_payer === FEE_PAYER.BUYER
    }

    throw new Error(`Invalid payment provider ${payment_provider}`)
}

function setWatilisted (eventTicketId) {
    return Db.query(
        `UPDATE "event_ticket" et
         SET "waitlisted" = TRUE 
         WHERE et.event_ticket_id = $1`,
        [eventTicketId]
    );
}

