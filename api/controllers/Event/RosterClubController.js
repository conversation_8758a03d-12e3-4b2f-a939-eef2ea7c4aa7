'use strict';

module.exports = {
    // get /api/v2/event/:event/club/:club/teams/staff
    teams_members: function (req, res) {
        var $roster_club_id = +req.params.club;
        if(!$roster_club_id) return res.status(400).json({error: 'No club provided'});
        var $event_id = +req.params.event;
        if(!$event_id) return res.status(400).json({error: 'No event provided'});

        var query = 
            `SELECT  
                 ( SELECT array_to_json(array_agg(row_to_json(teams)))       
                   FROM ( 
                        SELECT   
                            rt.roster_team_id, rt.age, rt.gender, rt.master_team_id,  
                            rt.organization_code, rt.team_name,  
                            d.short_name AS division_name,  
                            (  
                                SELECT array_to_json(array_agg(row_to_json(s)))  
                                FROM (  
                                    SELECT
                                        FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last)) "name",
                                        ms.gender,
                                        ms.phone "phonem", 
                                        ms.phoneh, 
                                        ms.phoneo, 
                                        ms.phonew, 
                                        ms.email, 
                                        ms.is_impact,
                                        (
                                            e.online_team_checkin_mode = 'primary_staff_barcodes' 
                                            AND COALESCE(rsr.primary, msr.primary)
                                            AND rt.online_checkin_date IS NOT NULL
                                        ) "primary_staff_barcodes_checkin",
                                        ms.checkin_barcode "barcode",
                                        (
                                            CASE 
                                              WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                                              WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                                              ELSE NULL
                                            END  
                                         ) "cert",
                                         ms.address, 
                                         ms.state, 
                                         ms.city, 
                                         ms.zip, 
                                         ( SELECT etc.deactivated_at IS NOT NULL
                                            FROM event_team_checkin AS etc
                                            WHERE etc.event_id = rt.event_id
                                                AND etc.master_staff_id = ms.master_staff_id
                                            LIMIT 1
                                         ) "is_deactivated",
                                         ms.master_staff_id "id", 
                                         ms.safesport_statusid,
                                         rsr.roster_team_id,
                                         COALESCE(r.short_name, mr.short_name, 'N/A') "role_name",
                                         COALESCE(r.role_id, mr.role_id) "role_id",
                                         COALESCE(rsr.primary, msr.primary) "primary"
                                    FROM master_staff ms  
                                    LEFT JOIN master_staff_role msr   
                                        ON msr.master_staff_id = ms.master_staff_id       
                                        AND msr.master_team_id = rt.master_team_id        
                                    LEFT JOIN roster_staff_role rsr 
                                        ON rsr.master_staff_id = ms.master_staff_id 
                                        AND rsr.master_team_id = rt.master_team_id 
                                    LEFT JOIN "roster_team" rt 
                                        ON rt.roster_team_id = rsr.roster_team_id 
                                    LEFT JOIN "role" r 
                                        ON r.role_id = msr.role_id                 
                                    LEFT JOIN "role" mr
                                        ON mr.role_id =  rsr.role_id
                                    WHERE rsr.deleted IS NULL
                                    AND rsr.deleted_by_user IS NULL
                                    AND ms.deleted IS NULL 
                                    AND rt.roster_club_id = $2
                                    AND rt.event_id = $1
                                    ORDER BY COALESCE(r.sort_order, mr.sort_order),
                                    COALESCE(r.sort_order, mr.sort_order)    
                                ) s  
                            ) AS staff  
                        FROM roster_team rt  
                        LEFT JOIN master_team mt  
                            ON mt.master_team_id = rt.master_team_id  
                        LEFT JOIN division d  
                            ON d.division_id = rt.division_id  
                        JOIN event e ON e.event_id = rt.event_id    
                        WHERE rt.deleted IS NULL  
                        AND rt.roster_club_id = $2 
                        AND rt.event_id = $1 
                  ) teams 
               ) AS teams, 
                  ( 
                        SELECT array_to_json(array_agg(row_to_json(staff))) 
                        FROM ( 
                              SELECT ms.first, ms.last, ms.gender, ms.email, 
                                    COALESCE(ms.phone, ms.phonew, ms.phoneh, ms.phoneo) AS phone, 
                                    r.name AS role_name 
                              FROM master_staff ms 
                              LEFT JOIN master_staff_role msr 
                                    ON ms.master_staff_id = msr.master_staff_id 
                              LEFT JOIN "role" r 
                                    ON r.role_id = msr.role_id                 
                              LEFT JOIN roster_club rc 
                                    ON rc.master_club_id = ms.master_club_id 
                                    AND rc.event_id = $1 
                                    AND rc.roster_club_id = $2 
                              WHERE msr.role_id IN (2,3,11) 
                              AND rc.roster_club_id = $2 
                              AND ms.deleted IS NULL 
                              ORDER BY r.sort_order ASC                  
                        ) staff 
                  ) AS club_staff`;

        Db.query(query, [$event_id, $roster_club_id]).then(function (result) {
            let data = (result && result.rows && result.rows[0]);

            if(!_.isEmpty(data.teams) && data.teams.length) {
                data.teams.forEach(t => {
                    if(!_.isEmpty(t.staff)) {
                        t.staff = CheckInRosterService.addDescriptionLinks(t.staff, $event_id);
                    }
                })
            }

            res.status(200).json(data);
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/v2/event/:event/club/teams/available
    availableTeams: function (req, res) {
        var $eventId    = parseInt(req.params.event, 10),
            $clubCode   = req.query.club,
            $teamCode   = req.query.team,
            season      = sails.config.sw_season.current,
            condition   = '',
            params      = []

        if(!$clubCode && !$teamCode) return res.validation('Empty parameters passed');
        if(!$eventId)                return res.validation('Invalid event identifier');

        params.push($eventId, season);

        if($clubCode) {
            params.push(`%${$clubCode}%`)
            condition += `AND mc.code ILIKE $` + params.length;
        }
        if($teamCode) {
            params.push(`%${$teamCode}%`)
            condition += `
            AND mt.organization_code ILIKE $` + params.length;
        }

        Db.query(
            `SELECT 
                mt.age, mt.gender, mt.team_name, mt.organization_code "team_code", 
                (rt.roster_team_id IS NOT NULL) "entered", COALESCE(rt.rank, mt.rank) "rank",
                mc.club_name, mc.code "club_code", mt.master_team_id, mc.master_club_id
            FROM "master_team" mt
            LEFT JOIN "roster_team" rt
                ON rt.master_team_id = mt.master_team_id
                AND rt.event_id = $1
                AND rt.deleted IS NULL
            LEFT JOIN "master_club" mc
                ON mc.master_club_id = mt.master_club_id
            WHERE mt.season = $2
                AND mt.deleted IS NULL
                ${condition}
            ORDER BY (CASE WHEN rt.roster_team_id IS NULL THEN TRUE ELSE FALSE END), mc.club_name`,
            params
        ).then(result => {
            res.status(200).json({ teams: result.rows })
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // post /api/v2/event/:event/club/teams/assign
    assignTeam: async function (req, res) {
        const $eventId      = Number(req.params.event),
            $masterTeamId   = Number(req.body.team_id),
            $masterClubId   = Number(req.body.club_id),
            $divisionId     = Number(req.body.division_id);

        if(!$eventId) {
            return res.validation('Invalid event identifier');
        }

        if(!$masterTeamId) {
            return res.validation('Invalid team identifier passed');
        }

        if(!$masterClubId) {
            return res.validation('Invalid club identifier passed');
        }

        if(!$divisionId) {
            return res.validation('Invalid division identifier passed');
        }

        try {
            await ClubService.eventTeamRegistration.assignTeam($eventId, $masterTeamId, $masterClubId, $divisionId);

            return res.ok();
        } catch (err) {
            return res.customRespError(err);
        }
    },

    // post /api/v2/event/:event/club/set-local
    setLocal: async function (req, res) {
        try {
            const eventId                       = Number(req.params.event);
            const eoID                          = req.session.passport.user.event_owner_id;
            const { isLocal, notes = null }     = req.body;

            const params                = _.defaults(_.omit(req.body, 'limit', 'page', 'revert'), { event: eventId });
            const query                 = squel.select().from('roster_team', 'rt');
            const options               = { hideOrderBy: true };

            const subSql = _queryBuilder({ query, params, options });
            subSql.field('rt.roster_club_id');
            subSql.distinct();

            const sql = squel.update().table('roster_club')
                .set('is_local', isLocal)
                .where(`roster_club_id IN (${subSql})`)
                .returning('roster_club_id');

            const result = await Db.query(sql);

            if (isLocal) {
                await HousingService.updateHousingStatusChangedAt({
                    eventID: eventId,
                    rosterClubID: result.rows.map(({roster_club_id}) => roster_club_id),
                });
            }

            if (result.rows.length) {
                const action                = getAction(isLocal);
                const _addNotif             = addNotif.bind(null, eventId, eoID, action, notes);
                const _updateClubHousing    = updateClubHousing.bind(null);

                await Promise.all(result.rows.map(_updateClubHousing));

                loggers.debug_log.verbose('Adding notification for', result.rows.length, 'clubs');
                await Promise.all(result.rows.map(_addNotif));
            }

            res.ok();
        } catch (error) {
            res.customRespError(error);
        }
    }
};

function _queryBuilder ({ query, params, fields, method, options }) {
    return SQLQueryBuilder.clubs.list(query, params, fields, method, options);
}

function getAction(status) {
    return status ? 'club.marked.local' : 'club.marked.notlocal';
}

function addNotif(eventID, eventOwnerID, action, notes, club) {
    return eventNotifications.add_notification(eventID, {
        roster_club_id  : club.roster_club_id,
        event_owner_id  : eventOwnerID,
        action          : action,
        comments        : notes
    });
}

function updateClubHousing({ roster_club_id }) {
    return HousingService.update_club_housing({rosterClubID: roster_club_id}, (err) => {
        if (err) {
            throw err;
        }
    })
}
