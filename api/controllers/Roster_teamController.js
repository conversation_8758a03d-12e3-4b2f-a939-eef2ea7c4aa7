'use strict';

var createSchema = require('json-gate').createSchema;

const co = require('co');
const swUtils = require('../lib/swUtils');
const { updateQualification: updateQualificationSchema, updateTeam } = require('../validation-schemas/roster_team');

require('date-utils');

module.exports = {
    // put /api/event/:event/roster_team/:id
    update: async function (req, res) {
        const eventID = parseInt(req.params.event, 10),
            rosterTeamID = parseInt(req.params.id, 10),
            data = req.body,
            userId = parseInt(req.session.passport.user.user_id);

        if(!eventID) {
            return res.validation('Invalid event identifier passed');
        }
        if(!rosterTeamID) {
            return res.validation('Invalid team identifier passed');
        }

        try {
            const rosterTeam = await RosterTeamService.team.update.process(eventID, rosterTeamID, data, userId);

            res.status(200).json({ roster_team: rosterTeam });
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/event/:event/roster_teams/payment
    teams_to_pay: function (req, res) {
        let event_id    = parseInt(req.params.event, 10),
            roster_club = parseInt(req.query.roster_club, 10),
            season      = sails.config.sw_season.current;

        let query = squel.select().from('roster_team', 'rt');

        query.field('rt.roster_team_id')
          .field('rt.team_name')
          .field('rt.roster_club_id')
          .field('d.short_name', 'division_name')
          .field('COALESCE(rt.discount, 0)', 'discount')
          .field('rt.status_entry')
          .field('d.reg_fee','div_reg_fee')
          .field('d.division_id')
          .field('rt.status_paid')
          .field('rt.organization_code', 'code')
          .field('rt.gender')
        // this "case-when" is useless, we can just return 0 for this query (WHY? 😳)
        // TEMPORARY solution !!!
        // TODO: add check for canceled payments
          .field(
            `(
                CASE 
                    WHEN rt.status_paid != 22 
                    THEN 0
                    ELSE (
                        SELECT SUM(pt.amount) 
                        FROM purchase_team pt 
                        WHERE rt.roster_team_id = pt.roster_team_id
                    )
                END
            ) "paid"`);
        query.left_join('division', 'd', 'd.division_id = rt.division_id');
        query.join('master_team', 'mt', 'mt.master_team_id = rt.master_team_id');

        query.where('rt.event_id= ? AND rt.deleted IS NULL AND rt.status_paid != 22 AND rt.status_paid != 24', event_id);
        query.where('rt.status_entry <> 11');
        query.where('mt.season = ?', season);

        query.order('rt.organization_code');   

        if(roster_club) {
            query.where('rt.roster_club_id = ? ', roster_club);
        }   

        Db.query(query).then(result => {
          let teams = result.rows.map(team => {
              team.discount = Number(team.discount);

              return team;
          });

          res.status(200).json({ teams });

        }).catch(res.customRespError.bind(res));
     },

    //get /api/event/:event/roster_team/:id/history
    team_history:  async function (req, res) {
        const roster_team_id = parseInt(req.params.id, 10);
        const event_id = parseInt(req.params.event, 10);

        if (!roster_team_id) return res.status(400).json({ error: 'No team selected' });

        try {
            const result = await RosterTeamService.history.getData(roster_team_id, event_id);

            return res.status(200).json({ history: result });
        }
        catch (err) {
            res.customRespError(err)
        }
    },
    // post /api/event/:event/roster_team/:id/housing
    update_housing: function(req, res) {
        const rosterTeamID  = Number(req.params.id);
        const eventID       = Number(req.params.event);
        const eventOwnerID  = eventOwnerService.findId(eventID, req.user);

        if (!rosterTeamID) {
            return res.validation('Invalid team identifier');
        }

        if (!eventID) {
            return res.validation('Invalid event identifier');
        }

        return HousingService.local.updateIsLocal(eventID, rosterTeamID, eventOwnerID, req.body)
            .then(updated_roster_teams => res.status(200).json({updated_roster_teams}))
            .catch(res.customRespError.bind(res))
    },
    // get /api/event/:event/roster_team/:id/booking
    booking: function (req, res) {
        let teamId    = parseInt(req.params.id, 10),
            event_id  = parseInt(req.params.event, 10);

        if(!teamId) return res.validation('Invalid team identifier');
        if(!event_id) return res.validation('Invalid event identifier');

        Db.query(
            `SELECT 
                tb.ths_id, tb.ths_hotel_name, tb.ths_hotel_status, tb.ths_tentative_nights, 
                tb.ths_confirmed_nights, tb.ths_loyalty, 
                COALESCE(TO_CHAR(tb.ths_contract_issued::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_contract_issued, 
                COALESCE(TO_CHAR(tb.ths_when_accepted::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_when_accepted,
                COALESCE(TO_CHAR(tb.ths_when_canceled::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_when_canceled ,
                (COUNT(h.*) > 1) "modified"
            FROM ths_booking tb 
            LEFT JOIN event e ON e.event_id = tb.event_id 
            LEFT JOIN "ths_history" h 
                ON h.ths_id = tb.ths_id
            WHERE tb.roster_team_id = $1
                AND tb.event_id = $2
            GROUP BY tb.ths_id, tb.ths_hotel_name, tb.ths_hotel_status, tb.ths_tentative_nights, 
                tb.ths_confirmed_nights, tb.ths_loyalty, e.timezone, tb.ths_contract_issued, tb.ths_when_accepted, 
                tb.ths_when_canceled, tb.modified
            ORDER BY tb.modified`,
            [teamId, event_id]
        ).then(result => {
            res.status(200).json(result.rows);
        }).catch(res.customRespError.bind(res));
    },

    history: function(req, res) {

        var teamId = Number(req.params.id),
            thsId = Number(req.params.ths_id);

        if (isNaN(teamId) || isNaN(thsId)) {
            return res.notFound();
        }

        let q = 
            ` SELECT 
                    th.ths_id, th.ths_hotel_name, th.ths_hotel_status, th.ths_tentative_nights,
                    th.ths_confirmed_nights, th.ths_loyalty, 
                    coalesce(to_char(th.ths_contract_issued::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_contract_issued, 
                    coalesce(to_char(th.ths_when_accepted::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_when_accepted, 
                    coalesce(to_char(th.ths_when_canceled::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') ths_when_canceled,
                    coalesce(to_char(th.modified::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon, DD YYYY HH12:MI AM'), 'N/A') "modified"
            FROM ths_history th 
            INNER JOIN "event" e 
                ON e."event_id" = th."event_id"
            WHERE th.roster_team_id = $1 AND th.ths_id = $2 
            ORDER BY th.ths_modified`;

        let p = [teamId, thsId];

        Db.query(q, p)
        .then(result => {
            let historyRows = result.rows;

            if (result.rows.length > 0) {
                let initialHistoryRow = historyRows[0];
                initialHistoryRow.modified = 'N/A';
            }

            res.status(200).json(historyRows);
        }).catch(res.customRespError.bind(res));
    },
    // post /api/event/:event/roster_teams/email
    send_email: async function(req, res) {
        const eventId = Number(req.params.event);

        if(!eventId) {
            return res.validation('No event selected');
        }

        try {
            await RosterTeamService.rosterTeamEmail.sendEmail(eventId, req.body);

            return res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    // GET /api/v2/event/:event/teams/current-teams
    getLatestTeamsData: function (req, res) {
        const eventId = parseInt(req.params.event, 10);
        const teams = req.query.teams;

        if(!eventId) return res.status(400).json({error: 'No event identifier specified'});

        if (!teams || !teams.length) return res.status(200).json({});

        Db.query(`SELECT rt.status_checkin, rt.roster_team_id, rt.team_alert_note
                    FROM roster_team "rt" 
                    WHERE rt.roster_team_id IN (${teams})`)
            .then( result => {
                res.status(200).json({ teams: result.rows });
            })
            .catch(err => {
                res.customRespError(err);
            });
    },

    //GET /api/event/:event/teams/checkin
    checkInAll: function (req, res) {

        const ORDER_ALIASES = {
            'team_name'                 : 'rt.team_name',
            'organization_code'         : 'rt.organization_code',
            'division_name'             : 'd.name',
            'club_name'                 : 'rc.club_name'
        };

        const eventId = parseInt(req.params.event, 10);

        if(!eventId) return res.status(400).json({error: 'No event identifier specified'});
        
        let sqlParams = _.defaults(req.query, { page: 1, limit: 100, event: eventId });
    
        sqlParams.limit = parseInt(sqlParams.limit, 10);
        sqlParams.page = parseInt(sqlParams.page, 10);
        
        if(!sqlParams.limit || swUtils.isNumberLessThanZero(sqlParams.limit)) {
            sqlParams.limit = 100;
        }
        if(!sqlParams.page || swUtils.isNumberLessThanZero(sqlParams.page)) {
            sqlParams.page = 1;
        }
        
        const limit         = sqlParams.limit * (sqlParams.page || 1);
        const orderBy       = sqlParams.order;
        const sortDirection = sqlParams.direction;
        const division      = sqlParams.division;
        const checkin       = sqlParams.checkin;
        const onlineCheckin = sqlParams.online_checkin;
        const search        = swUtils.escapeStr(sqlParams.search || '');

        let fields = [
            { column: 'rt.roster_team_id'},
            { column: 'rt.team_alert_note'},
            { column: 'rt.team_name'},
            { column: 'rt.organization_code'},
            { column: 'rt.gender'},
            { column: 'rt.division_id'},
            { column: `TO_CHAR (rt.online_checkin_date::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD')`, alias: 'online_checkin_date'},
            { column: `TO_CHAR (rt.online_checkin_date::TIMESTAMPTZ AT TIME ZONE e.timezone, 'HH12:MI AM')`, alias: 'online_checkin_time'},
            { column: `COALESCE(rt.status_checkin, 'notcheckedin')`, alias: 'status_checkin'},
            { column: 'd.name', alias: 'division_name'},
            { column: 'rc.club_name'},
            { column: 'mc.director_first'},
            { column: 'mc.director_last'},
            { column: 'mc.director_phone'},
            { column: 'rt.wristbands_count_staff'},
            { column: 'rt.wristbands_count_athletes'},
            { column: 'count(rt.*) OVER()::INT', alias: 'count'},
            { column: `
            ( SELECT CASE 
                        WHEN rt.status_checkin = 'checkedin' THEN max(ec.created) 
                        ELSE NULL 
                     END
                FROM event_change ec
                WHERE e.event_id = ec.event_id
                      AND ec.roster_team_id = rt.roster_team_id
                      AND ec.action = 'team.checkin.checkedin'
              )
            `, alias: 'checkin_date'},
            { column:
            `(
                SELECT array_to_json(array_agg(row_to_json(checkin_staff)))
                FROM (
                    SELECT
                        ms.phone, ms.phoneo, ms.phoneh, ms.phonew,
                        ms.first, ms.last, ms.email, etc.staff_type,
                        ms.checkin_barcode barcode
                    FROM master_staff ms
                    LEFT JOIN event_team_checkin etc
                        ON ms.master_staff_id = etc.master_staff_id
                    WHERE etc.roster_team_id = rt.roster_team_id
                ) checkin_staff
            )`, alias: 'online_checkin_staff'},
            { column:
            `(
                SELECT array_to_json(array_agg(row_to_json(team_staff)))
                FROM (
                    SELECT
                        msr.primary, ms.phone, ms.phoneo, ms.phoneh, ms.phonew,
                        r.name AS role_name, ms.first, ms.last, ms.email
                    FROM master_staff_role msr
                    LEFT JOIN master_staff ms
                        ON msr.master_staff_id = ms.master_staff_id
                    LEFT JOIN ROLE r
                        ON r.role_id = msr.role_id
                    WHERE msr.master_team_id = rt.master_team_id
                ) team_staff
            )`, alias: 'staff'},
            { column:
            `(
                SELECT array_to_json(array_agg(row_to_json(status_comments)))
                FROM (
                    SELECT ec.comments AS note,
                    (
                        CASE
                             WHEN ec.action = 'team.checkin.pending' THEN 'pending'
                             WHEN ec.action = 'team.checkin.checkedin' THEN 'checkedin'
                             WHEN ec.action = 'team.checkin.notcheckedin' THEN 'notcheckedin'
                             WHEN ec.action = 'team.checkin.alert' THEN 'alert'

                        END
                    ) AS status
                    FROM event_change ec
                    WHERE ec.roster_team_id = rt.roster_team_id
                    AND ec.comments IS NOT NULL
                    AND ec.comments <> ''
                    AND ec.action IN ('team.checkin.pending', 'team.checkin.checkedin', 'team.checkin.notcheckedin', 'team.checkin.alert')
                ) status_comments
            )`, alias: 'notes'}
        ];

        let _query = squel.select().from('roster_team', 'rt');

        _query.left_join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id');
        _query.left_join('division', 'd', 'd.division_id = rt.division_id');
        _query.left_join('event', 'e', 'e.event_id = rt.event_id');
        _query.left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id');
        _query.where('rt.event_id = ?', eventId);
        _query.where('rt.status_entry = 12 AND rt.deleted IS NULL');
        _query.limit(limit);

        if (orderBy && (sortDirection === 'asc' || sortDirection === 'desc')) {
            _query.order(ORDER_ALIASES[orderBy], sortDirection==='asc');
        } else {
            _query.order("rt.team_name", false);
        }

        if(Array.isArray(division) && division.length) {
            _query.where(`rt.division_id IN (${swUtils.numArrayToString(division)})`);
        }

        if(Array.isArray(onlineCheckin) && onlineCheckin.length) {

            /**
             *  'locked' and 'not_locked' are not mutually exclusive. If they both will be in query, query will return
             *  empty data. So we need skip this step if filter '$members' contains each of them.
             */
            if (onlineCheckin.indexOf('locked') === -1 || onlineCheckin.indexOf('not_locked') === -1) {
                if (onlineCheckin.indexOf('locked') >= 0) {
                    _query.where(`rt.locked IS TRUE`);
                }
                if (onlineCheckin.indexOf('not_locked') >= 0) {
                    _query.where(`rt.locked IS NOT TRUE`);
                }
            }

            /**
             *  'online_checkin' and 'no_online_checkin' are not mutually exclusive. If they both will be in query, query will return
             *  empty data. So we need skip this step if filter '$members' contains each of them.
             */
            if (onlineCheckin.indexOf('online_checkin') === -1 || onlineCheckin.indexOf('no_online_checkin') === -1) {
                if (onlineCheckin.indexOf('online_checkin') >= 0) {
                    _query.where(`rt.online_checkin_date IS NOT NULL`);
                }
                if (onlineCheckin.indexOf('no_online_checkin') >= 0) {
                    _query.where(`rt.online_checkin_date IS NULL`);
                }
            }
        }

        if(search) {
            let formattedSearch = '%' + search + '%';

            let expr = squel.expr()
                .and('rt.team_name ILIKE ?', formattedSearch)
                .or('rt.organization_code ILIKE ?', formattedSearch)
                .or('d.name ILIKE ?', formattedSearch)
                .or('rc.club_name ILIKE ?', formattedSearch);

            _query.where(expr);
        }

        if(Array.isArray(checkin) && checkin.length) {
            let expr = squel.expr();

            if (checkin.indexOf('checkedin') >= 0) {
                expr.or(`rt.status_checkin = 'checkedin'`);
            }
            if (checkin.indexOf('notcheckedin') >= 0) {
                expr.or(`rt.status_checkin != 'checkedin' OR rt.status_checkin IS NULL`);
            }
            if (checkin.indexOf('pending') >= 0) {
                expr.or(`rt.status_checkin = 'pending'`);
            }
            if (checkin.indexOf('alert') >= 0) {
                expr.or(`rt.status_checkin = 'alert'`);
            }
            _query.where(expr);
        }

        if(fields && fields.length) {
            _.each(fields, function (el) {
                _query.field(el.column, el.alias);
            })
        }

        Db.query(_query)
        .then(result => {
            if (_.isEmpty(result.rows)) {
                return res.status(200).json({});
            }

            return CheckInRosterService.validateCheckIn(eventId, result.rows).then(function(validation) {
                if (!_.isEmpty(validation)) {
                    result.rows.forEach((team, index) => {
                        if (typeof validation[index] !== 'undefined' && !_.isEmpty(validation[index].errors.roster))
                            result.rows[index].validation_errors = validation[index].errors;
                        if(team.online_checkin_staff) {
                            team.online_checkin_staff.forEach((staff) => {
                                const { barcode } = staff;
                                if(barcode) {
                                    staff.checkin_description_link = OnlineCheckinService.barcodes.getDescriptionLink({
                                        barcode,
                                        event_id: eventId
                                    });
                                }
                            });
                        }
                    });
                }

                res.status(200).json({ teams: result.rows });
            });

        }).catch(err => {
          res.customRespError(err);
        });
    },
    // post /api/event/:event/teams/setcheckstatus
    saveCheckInStatus: function (req, res) {
        let $eventID    = req.params.event,
            $teams      = req.body.teams,
            $notes      = req.body.notes || null,
            $alertNotes = req.body.team_alert_note || null,
            $status     = req.body.status;

        let eventOwnerID = eventOwnerService.findId($eventID, req.user);

        if (!$status) {
            return res.validation('Status Required');
        }

        if (!Array.isArray($teams) || $teams.length === 0) {
            return res.validation('No teams passed');
        }

        if($status !== 'alert') $alertNotes = null;

        co(function* () {
            let tr = yield (Db.begin());

            let teamsStr = swUtils.numArrayToString($teams);

            let updatedTeams = yield (tr.query(
               `UPDATE "roster_team" rt 
                SET status_checkin = $1, team_alert_note = $2
                WHERE rt.roster_team_id IN (${teamsStr}) 
                AND rt.event_id = $3
                RETURNING rt.roster_team_id, rt.status_checkin, rt.team_alert_note`,
               [$status, $alertNotes, $eventID]
            ).then(result => { return result.rows; }));

            if (updatedTeams.length !== $teams.length) {
                tr.rollback();
                return Promise.reject({ validation: 'Team not found' });
            }

            let statusTitle;

            if($status === 'checkedin') {
                statusTitle = 'team.checkin.checkedin';
            } else if($status === 'notcheckedin') {
                statusTitle = 'team.checkin.notcheckedin';
            } else if($status === 'pending'){
                statusTitle = 'team.checkin.pending';
            } else {
                statusTitle = 'team.checkin.alert';
            }

            let changes = yield (tr.query(
                `INSERT INTO "event_change" (
                    "roster_club_id", "division_id", "event_id", "comments", 
                    "event_owner_id", "roster_team_id", "action"
                ) 
                SELECT 
                  rt.roster_club_id, rt.division_id, rt.event_id, 
                  $1, $2, rt.roster_team_id, $3 
                FROM roster_team rt 
                WHERE rt.roster_team_id IN (${teamsStr}) 
                  AND rt.event_id = $4
                RETURNING roster_team_id, 
                TO_CHAR(created::TIMESTAMP AT TIME ZONE (SELECT timezone FROM event WHERE event_id = $4), 
                                                        'MM/DD HH12:MI AM') "checkin_date"`,
                [$notes, eventOwnerID, statusTitle, $eventID]
            ));

            yield (tr.commit());

            updatedTeams.forEach( team => {
                changes.rows.forEach( change => {
                    if(team.status_checkin === 'checkedin' && team.roster_team_id === change.roster_team_id) {
                        Object.assign(team, change);
                    }
                })
            });

            return updatedTeams;
        }).then(teams => {
            res.status(200).json({teams});
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // put /api/event/:event/roster_team/:id/update/qualification
    updateQualification: async function(req, res) {
        try {
            const eventID       = Number(req.params.event);
            const rosterTeamID  = Number(req.params.id);

            const validation = updateQualificationSchema.validate(req.body);

            if (!eventID) {
                throw { validation: 'Invalid event identifier passed' };
            }

            if (!rosterTeamID) {
                throw { validation: 'Invalid team identifier passed' };
            }

            if (validation.error) {
                throw { validationErrors: validation.error.details };
            }

            await RosterTeamService.prevQualification.updateTeamQualification(rosterTeamID, req.body);

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },

    //PUT /api/event/:event/roster_team/update/qualification
    acceptBidForTeams: async function (req, res) {
        const eventID = Number(req.params.event);
        const { teams, sender } = req.body || {};

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(_.isEmpty(teams)) {
            return res.validation('Teams object required');
        }

        if(!sender) {
            return res.validation('Sender required');
        }

        try {
            await RosterTeamService.prevQualification.setBidAcceptedForTeams(eventID, teams, sender);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
};

function _notifyDevTeam(oldTeamData, currentTeamData) {
    //console.log(currentTeamData)
    return co(function* () {
        // if status_paid != paid (22) and != pending (24)-- do not notify
        if (oldTeamData.status_paid !== 22 && oldTeamData.status_paid !== 24) return;

        // if reg_fee is the same -- do not notify
        if (Math.round((oldTeamData.reg_fee - currentTeamData.reg_fee) * 100 / 100 == 0)) return;

        return EmailService.renderAndSend({
            template: 'dev/team_changed_division',
            data: {
                old     : oldTeamData,
                current : currentTeamData
            },
            from: 'SportWrench <<EMAIL>>',
            to: 'Eugene Tichenor <<EMAIL>>',
            subject: `Event: ${oldTeamData.event_long_name}: Team <${oldTeamData.team_name}> 's division changed to a different reg fee`
        });


    })
}


