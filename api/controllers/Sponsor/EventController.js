const { sponsorRegistrationInfoSchema} = require('../../validation-schemas/event_sponsor');

module.exports = {
    // GET /api/sponsor/events
    get: async function (req, res) {
        try {
            const sponsorID = req.session.passport.user.sponsor_ids[0];

            if (!sponsorID) {
                throw { validation: 'Exhibitor/Sponsor profile not found' };
            }

            const events = await SponsorService.eventSponsor.getEvents({ sponsorID });

            res.json(events);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET /api/sponsor/event/:event/info
    getEventInfo: async function(req, res) {
        try {
            const eventID = Number(req.params.event);

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            const info = await SponsorService.registration.getEventInfo(eventID);

            res.json(info);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // POST /api/sponsor/event/:event
    create: async function(req, res) {
        const eventId = Number(req.params.event);
        const sponsorId = Number(req.session.passport.user.sponsor_ids[0]);
        const registrationInfo = req.body;

        if (!eventId) {
            return res.validation('Invalid Event Identifier');
        }

        if (!sponsorId) {
            return res.validation('Sponsor Identifier Required');
        }

        const userAccessIds = {
            sales_manager_id: Number(req.session.passport.user.sales_manager_id),
            user_id: Number(req.session.passport.user.user_id),
            sponsor_id: sponsorId
        };

        try {
            await SponsorService.registration.createRegistrationInfo(eventId, sponsorId, userAccessIds, registrationInfo);

            await SponsorService.notifications.sendAppliedNotification(eventId, sponsorId);

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },

    // PUT /api/sponsor/event/:event
    update: async function(req, res) {
        const eventId = Number(req.params.event);
        const sponsorId = Number(req.session.passport.user.sponsor_ids[0]);
        const registrationInfo = req.body;

        if (!sponsorId) {
            return res.validation('Sponsor Identifier Required');
        }

        if (!eventId) {
            return res.validation('Invalid Event Identifier');
        }

        try {
            const { error: joiValidationError }  = sponsorRegistrationInfoSchema.validate(registrationInfo);

            if (joiValidationError) {
                throw { validationErrors: joiValidationError.details };
            }

            await SponsorService.registration.update(eventId, sponsorId, registrationInfo);

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET /api/sponsor/event/:event
    getApplyingInfo: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const sponsorID = Number(req.session.passport.user.sponsor_ids[0]);

            if (!sponsorID) {
                throw { validation: 'Sponsor Identifier Required' };
            }

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            const info = await SponsorService.registration.getApplyingInfo({ eventID, sponsorID });

            res.json(info);
        } catch (e) {
            res.customRespError(e);
        }
    },

    // GET /api/sponsor/payments
    getPaymentsList: (req, res) => {
        const sponsorIDs = req.session.passport.user.sponsor_ids || [];

        return BoothsService.payment.getInvoicesList({ sponsorIDs })
            .then(payments => res.status(200).json({ payments }) )
            .catch(res.customRespError);
    }
}
