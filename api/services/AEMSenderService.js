'use strict';

/* jshint eqnull:true */

const swUtils   			= require('../lib/swUtils');
const VariablesTransformer 	= require('./aem-sender-utils/_VariablesTransformer.js')

const emailSchema   		= require('../validation-schemas/email');

class AEMSenderService {
    constructor () {}

    get ALLOWED_GROUPS_FOR_SENDING () {
        return [
            AEMService.CLUB_GROUP,
            AEMService.OFFICIAL_GROUP,
            AEMService.TICKET_GROUP,
            AEMService.TEAMS_PAYMENTS_GROUP,
            AEMService.EVENT_OWNERS_GROUP,
            AEMService.NEWS_LETTERS_GROUP,
            AEMService.CAMP_GROUP,
            AEMService.STAFF_GROUP,
            AEMService.HEAD_REFEREE_GROUP,
            AEMService.EXHIBITORS_GROUP,
            AEMService.BOOTHS_PAYMENTS_GROUP,
            AEMService.CLUBS_DIRECTORS_GROUP
        ];
    }

    get INFO_SW_BOX () {
        return '"SportWrench" <<EMAIL>>'
    }

    __validateLetterData__ (data) {
        let res = emailSchema.validate(data, { abortEarly: false });

        if (res.error) {
            throw { validationErrors: res.error.details };
        } else {
            return res.value;
        }
    }

    __getEventRegMethod__ (eventID) {
        if (!eventID) {
        return Promise.resolve();
        }

        return Db.query(
            squel.select().from('event')
              .field('registration_method')
            .where('event_id = ?', eventID)
        ).then(result => {
            let row = result.rows[0] || null;

            if (row === null) {
                return Promise.reject({ validation: 'Event not found' });
            }

            let reg_method = row.registration_method;

            if (!reg_method) {
                return Promise.reject({ validation: 'Corrupted Event Data' });
            }

            return reg_method;
        })
    }

    __getTemplate__ (eventOwnerID, eventID, templateID) {
        if (!templateID) {
            return Promise.resolve(null);
        }

        let getTmplPromise;

        if (eventOwnerID) {
            getTmplPromise = AEMService.getTemplate(eventOwnerID, eventID, templateID);
        } else {
            getTmplPromise = AEMService.getTemplateByID(templateID);
        }

        return getTmplPromise.then(template => {
            /* TODO: add check for "published" IS TRUE here */
            if (template === null || !template.is_valid) {
                return Promise.reject({ validation: 'Template is not available' });
            }

          	return {
                html 	: template.email_html,
                text 	: template.email_text,
                group 	: template.group,
                subject : template.subject
            };
        });
    }

    formatDestinationAddress (receiver) {
        const specialCharactersPattern  = /[,;<>]/g;
        const email                     = receiver.email;

        let receiverName = [receiver.first, receiver.last]
            .filter(v => v)
            .join(' ');

        if(receiverName.length === 0) {
            return email;
        }

        if(receiver.email.includes(EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING)) {
            let result = email.split(EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING)
                .map(e => `"${receiverName.replace(specialCharactersPattern, '')}" <${e}>`);

            return result.join(EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING);
        } else {
            return `"${receiverName.replace(specialCharactersPattern, '')}" <${email}>`;
        }
    }

    getRecipientType (group) {
        return ({
            [AEMService.CLUB_GROUP] 		    : 'club',
            [AEMService.OFFICIAL_GROUP] 	    : 'official',
            [AEMService.STAFF_GROUP] 	        : 'staff',
            [AEMService.TICKET_GROUP] 		    : 'ticket buyer',
            [AEMService.CAMP_GROUP] 		    : 'camp',
            [AEMService.CLUB_DIRECTORS_GROUP]   : 'club',
            [AEMService.CLUB_STAFF_GROUP]       : 'club',
            [AEMService.TICKETS_PAYMENTS_GROUP] : 'ticket buyer',
            [AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP] : 'event owner',
            [AEMService.UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP] : 'upcoming fee charge',
        })[group];
    }

    async __saveLetterSendingToHistory__ (client, group, eventID, templateID, letter, receiver, emailID) {

        let insertToEventEmailTable = () => {
            return client.query(
                squel.insert().into('event_email').setFields({
                    email_subject       : letter.subject,
                    email_from          : letter.from,
                    email_to            : letter.to,
                    email_cc            : letter.cc,
                    email_bcc           : letter.bcc,
                    email_html          : letter.html,
                    email_text          : letter.text,
                    event_id            : eventID,
                    reason_type         : 'email',
                    recipient_type      : this.getRecipientType(group),
                    email_template_id   : templateID || null,
                    roster_club_id      : receiver.is_staff ? null : receiver.id,
                    master_staff_id     : receiver.is_staff ? receiver.id : null,
                    purchase_id         : receiver.purchase_id,
                    email_id            : emailID || null,
                }).returning('event_email_id')
            )
            .then(result => result.rows[0].event_email_id)
        }

        let insertToEventChangeTable = event_email_id => {
            return client.query(
                squel.insert().into('event_change')
                .setFields({
                    event_id        : eventID,
                    roster_club_id  : receiver.roster_club_id || receiver.is_staff === false ? receiver.id : null,
                    action          : 'Email sent',
                    event_email_id,
                })
            )
        }

        let tasks = [];

        const eventEmailID = await insertToEventEmailTable();
        tasks.push(insertToEventChangeTable(eventEmailID));
        await Promise.all(tasks);
    }

    async __compileAndSendLetters__ (receivers, sender, template, variables, eventID, templateID, group, needLog, addEOToBcc, isFirstBatch = true) {
        let transactions = ({
            email: EmailService.beginEmailTransaction(
                {
                    from: sender,
                    templateId: templateID,
                }
            ),
            general: await Db.begin({ skipErrAboutCommittedTr: true })
        });

        let __log__ = null;
        if(needLog) {
            __log__ = this.__saveLetterSendingToHistory__.bind(this, transactions.general, group, eventID, templateID);
        }

        let send_CC_and_BCC_once = false;

        try {
            const emailPromises = [];
            for(const receiver of receivers) {

                    if(!receiver.to && !receiver.email) {
                        return Promise.resolve();
                    }

                    if(addEOToBcc && receiver.eo_email) {
                        template.bcc = receiver.eo_email;
                    }

                const letter = {
                    variables: VariablesTransformer.getVariablesValues(group, template, variables, receiver),
                };
                letter.to 			= receiver.to      || this.formatDestinationAddress(receiver);
                letter.replyto 		= template.replyto || null;
                letter.cc 			= (!send_CC_and_BCC_once && isFirstBatch)  && template.cc   || null;
                letter.bcc 			= (!send_CC_and_BCC_once && isFirstBatch)  && template.bcc  || null;

                send_CC_and_BCC_once = true;

                emailPromises.push(
                    transactions.email.sendEmail(letter)
                        .then(({email_id}) => {
                            if (needLog) {
                                return __log__(letter, receiver, email_id)
                            }
                        })
                        .catch((err) => loggers.errors_log.error(err))
                );
            }
            await transactions.email.commit();
            await Promise.all(emailPromises);
            await transactions.general.commit();
        }
        catch(err){
            await Promise.all([
                rollback(transactions.email),
                rollback(transactions.general)
            ]);

            throw err;
        }

        function rollback (tr) {
            if (tr && !tr.isCommited) {
                return tr.rollback().catch(() => {});
            } else {
                return Promise.resolve();
            }
       }
    }

    /**
     * Sends email letter to receivers
     * @param {string} receiverGroup - The group of the receiver e.g. "club", "official", etc.
     * @param userID
     * @param eventID
     * @param eventOwnerID
     * @param templateID
     * @param letterData
     * @param receiverFiltersData
     * @param emailCategory
     */
    async sendLetters (receiverGroup, userID, eventID, eventOwnerID, templateID, letterData, receiverFiltersData = {}, emailCategory) {
        if (!receiverGroup) {
            throw { validation: 'No Receiver Type passed' };
        }

        if (this.ALLOWED_GROUPS_FOR_SENDING.indexOf(receiverGroup) === -1) {
            throw {
                validation: 'Sending for the specified receiver type is not implemented yet'
            };
        }

        if (eventID && !Number.isInteger(eventID)) {
            throw { validation: 'Invalid event ID' };
        }

        if (eventOwnerID && !Number.isInteger(eventOwnerID)) {
            throw { validation: 'No Access Rights' };
        }

        await (this.__validateLetterData__(letterData));

        let settings = await (async () => {
            const [
                regMethod,
                template,
                variables,
            ] = await Promise.all([
                this.__getEventRegMethod__(eventID),
                /* NOTE: there's no check for published template in the method */
                this.__getTemplate__(eventOwnerID, eventID, templateID),
                AEMService.getTemplateGroupVariables(receiverGroup, true),
            ]);
            return {
                regMethod,
                template,
                variables,
                emailCategory,
            };
        })();

        if (settings.template) {
            if (settings.template.group !== receiverGroup) {
                throw {
                    validation: `Template's receiver type does not match passed receivers type`,
                    description: `Got: "${receiverGroup}", DB: "${settings.template.group}"`
                };
            }
        } else {
            /* We expect to get html & text template from CKEditor if there is no template */
            if (!letterData.content || !letterData.raw_text) {
                throw { validation: 'Expecting HTML & TEXT versions of letter to be passed' };
            }

            settings.template = { html: letterData.content, text: letterData.raw_text };
        }

        settings.template.subject 	= letterData.subject || settings.template.subject;
        settings.template.replyto 	= letterData.replyto;
        settings.template.cc 		= letterData.cc;
        settings.template.bcc 		= letterData.bcc;

        let _hasUnknown = VariablesTransformer.checkTemplateForUnknownVariables(settings.template, settings.variables);

        if (_hasUnknown) {
            throw new Error('Template has unknown variables!');
        }

        VariablesTransformer.checkVariablesInTemplateSubject(settings.template.subject, settings.variables);

        let receiversGroups = this.__getReceiversGroups(receiverGroup, receiverFiltersData);

        if (receiverGroup === AEMService.EXHIBITORS_GROUP) {
            throw {
                validation: 'Sending for the specified receiver type is not implemented yet'
            };
        } else if (receiverGroup === AEMService.BOOTHS_PAYMENTS_GROUP) {
            throw {
                validation: 'Sending for the specified receiver type is not implemented yet'
            };
        }
        
        let jobData = this.__formatEmailsJobData(receiverFiltersData, settings);
        
        let tr;
        
        try {
            //We need to rollback template creation if email sending job creation failed
            if(!templateID) {
                tr = await Db.begin();
                
                templateID = await this.__createManualTemplate(tr, eventID, eventOwnerID, receiverGroup, settings);
            }
            
            await SystemJobService.createEmailSendingJob(receiversGroups, [eventID], templateID, userID, jobData);
            
            if(tr) {
                await tr.commit();
            }
        } catch (err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }
            
            throw err;
        }
    }

    /* TODO: not finished */
    async __findNotificationReceivers__ (group, eventID, receiverFilters, templateType) {
        let _q = null;
        const emailParams = {
            emailCategory: EmailService.emailCategory.EMAIL_CATEGORIES.transactional,
        };

        if (group === AEMService.CLUB_GROUP) {
            /* TODO */
            _q = SQLQueryBuilder.clubs.emailReceiversList(eventID, receiverFilters)
        } else if (group === AEMService.TICKET_GROUP) {
            _q = SQLQueryBuilder.ticketsCustomer.emailReceiversList(eventID, receiverFilters, emailParams)
        } else if (group === AEMService.CAMP_GROUP) {
            _q = SQLQueryBuilder.camps.emailReceiversList(eventID, receiverFilters, emailParams)
        } else if (group === AEMService.OFFICIAL_GROUP) {
            _q = SQLQueryBuilder.eventOfficial
                                    .notificationReceiversList(eventID, receiverFilters)
        } else if (group === AEMService.HEAD_REFEREE_GROUP) {
            _q = SQLQueryBuilder.headReferee
                                    .notificationReceiversList(eventID, receiverFilters)
        } else if (group === AEMService.TEAMS_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.teamsPayment
                                    .notificationReceiversList(eventID, receiverFilters)
        } else if(group === AEMService.STAFF_GROUP) {
            _q = SQLQueryBuilder.eventStaff
                                    .notificationReceiversList(eventID, receiverFilters)
        } else if (group === AEMService.TICKETS_REFUNDS_GROUP) {
            _q = SQLQueryBuilder.ticketsRefunds.emailReceiversList(eventID, receiverFilters, templateType)
        }
        else if (group === AEMService.TICKETS_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.ticketPayments.emailReceiversList(eventID, receiverFilters)
        }
        else if (group === AEMService.EXHIBITORS_GROUP) {
            _q = SQLQueryBuilder.exhibitors
                                    .notificationReceiversList(eventID, receiverFilters)
        }
        else if (group === AEMService.BOOTHS_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.boothsPayments
                                    .notificationReceiversList(eventID, receiverFilters)
        }
        else if (group === AEMService.TEAMS_REFUNDS_GROUP) {
            _q = SQLQueryBuilder.teamsRefunds.notificationReceiversList(eventID, receiverFilters, templateType)
        }
        else if (group === AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.teamsUncollectedFeePayments.notificationReceiversList(eventID, receiverFilters)
        }
        else if (group === AEMService.TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.ticketsUncollectedFeePayments.notificationReceiversList(eventID, receiverFilters)
        }
        else if (group === AEMService.UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP) {
            _q = SQLQueryBuilder.upcomingUncollectedFeePayments.notificationReceiversList(eventID, receiverFilters)
        }
        else {
            throw new Error(`Unknown group '${group}' for receivers list generation`);
        }

        const { rows: receivers } = await Db.query(_q);

        if (group === AEMService.TICKETS_REFUNDS_GROUP) {
            formatBasicTickets(receivers);
        }

        return receivers;
    }

    /**
     * Send an automatic system notification about a certain case
     *
     * @param  {string} group           - The group of Notification/Project Section
     * @param  {string} templateType    - An action name (e.g "official.accepted")
     * @param  {number} eventID         - An event's identifier
     * @param  {object} receiverFilters - Data to filer receivers list
     * @param  {boolean} skipErrEmail   - Do not send error email if sending fails
     * @param  {boolean} addEOToBcc     - Add EO email to trigger bcc field
     * @return {Promise}
     */
    async sendTriggerNotification (group, templateType, eventID, receiverFilters = null, skipErrEmail = false, addEOToBcc) {
        try {
            if (receiverFilters == null) {
                throw {
                    validation  : 'No receivers!',
                    description : `Empty Receiver Filters`
                };
            }

            const ERROR_DESCR = `Sending Notification for system action "${
                                                                templateType}" of "${group}" group`;

            let [variables, template, receivers] = await (Promise.all([
                AEMService.getTemplateGroupVariables(group, true),
                AEMService.triggers.getTemplate(group, templateType, eventID),
                this.__findNotificationReceivers__(group, eventID, receiverFilters, templateType)
            ]));

            if (template == null) {
                throw {
                    validation: 'Template not found',
                    description: ERROR_DESCR,
                    skipEmail: true,
                };
            }

            if (receivers.length === 0) {
                throw {
                    validation  : 'Receivers not found',
                    description : ERROR_DESCR
                };
            }

            receivers = await VariablesTransformer.general.getEventSocialLinks(eventID, receivers);

            let needLog = true;

            await (this.__compileAndSendLetters__(
                receivers, this.INFO_SW_BOX, template, variables, eventID, template.id, group, needLog, addEOToBcc
            ));
        }
        catch(err) {
            loggers.errors_log.error(err);

            if (!skipErrEmail && err.skipEmail !== true) {
                ErrorSender.defaultError(err);
            }

            throw err;
        }
    }

    async sendTestLetter (templateID, userID, eventID, eventOwnerID, templateHTML, subject, $receiver) {
        if (!Number.isInteger(templateID)) {
            throw { validation: 'Invalid Template Identifier' };
        }

        if (!Number.isInteger(userID)) {
            throw { validation: 'Invalid User ID' };
        }

        if (!$receiver || !$receiver.email) {
            throw { validation: 'Invalid Receiver Email' };
        }

        let template = await (this.__getTemplate__(eventOwnerID, eventID, templateID));

        if (template === null) {
            throw { validation: 'Template not found' };
        }

        template.subject = subject || template.subject || 'Test Email Sending';

        if (templateHTML) {
            template.html = templateHTML;
            template.text = AEMService.stripHTML(templateHTML);
        }

        let receiver,compiled;

        if (!template.group) {
            /**
            * Basic Layouts have no "group" filled, so we cannot get a test receiver
            * and check the template for unknown variables
            */
                receiver = {
                first 	: $receiver.first || '',
                last 	: $receiver.last  || '',
                email 	: $receiver.email
            };
            compiled 	= template;
        } else if (this.ALLOWED_GROUPS_FOR_SENDING.indexOf(template.group) >= 0) {

            let variables = await (AEMService.getTemplateGroupVariables(template.group, true));

            let _hasUnknown = VariablesTransformer.checkTemplateForUnknownVariables(template, variables);

            if (_hasUnknown) {
                throw new Error('Template has unknown variables!');
            }

            if (template.group === AEMService.CLUB_GROUP) {
                receiver = await SQLQueryBuilder.clubs.getTestReceiver(eventID);
            } else if (template.group === AEMService.OFFICIAL_GROUP) {
                receiver = await SQLQueryBuilder.eventOfficial.getTestReceiver(eventID);
            } else if (template.group === AEMService.TICKET_GROUP) {
                receiver = await SQLQueryBuilder.ticketsCustomer.getTestReceiver(eventID);
            } else if (template.group === AEMService.CAMP_GROUP) {
                receiver = await SQLQueryBuilder.camps.getTestReceiver(eventID);
            } else if (template.group === AEMService.TEAMS_PAYMENTS_GROUP) {
                receiver = await SQLQueryBuilder.teamsPayment.getTestReceiver(eventID);
            } else if (template.group === AEMService.EVENT_OWNERS_GROUP) {
                receiver = await SQLQueryBuilder.eventOwners.getTestReceiver(eventID);
            } else if (template.group === AEMService.NEWS_LETTERS_GROUP) {
                receiver = {};
            } else if (template.group === AEMService.STAFF_GROUP) {
                receiver = await SQLQueryBuilder.eventStaff.getTestReceiver(eventID);
            } else if (template.group === AEMService.HEAD_REFEREE_GROUP) {
                receiver = await SQLQueryBuilder.headReferee.getTestReceiver(eventID);
            } else if (template.group === AEMService.EXHIBITORS_GROUP) {
                receiver = await SQLQueryBuilder.exhibitors.getTestReceiver(eventID);
            } else if (template.group === AEMService.BOOTHS_PAYMENTS_GROUP) {
                receiver = SQLQueryBuilder.boothsPayments.getTestReceiver(eventID)
            }

            receiver = await VariablesTransformer.general.getEventSocialLinks(eventID, receiver);

            receiver.first 	= $receiver.first || '';
            receiver.last 	= $receiver.last  || '';
            receiver.email 	= $receiver.email; 

            compiled = VariablesTransformer.transformVariables(
                template.group, 
                template, 
                variables, 
                receiver
            );
        } else {
            throw { validation: 'Sending for the specified receiver type is not implemented yet' };
        }

        let foundTestData = false;
        for(const {field} of VariablesTransformer.TMPL_FIELDS_CONTAINING_VARIABLES) {
            if(!(field in compiled) || typeof compiled[field] !== 'string') {
                continue;
            }
            if(!foundTestData) {
                foundTestData = SQLQueryBuilderUtils.hasTestDataSignature(compiled[field]);
            }
            if(foundTestData) {
                compiled[field] = SQLQueryBuilderUtils.removeTestDataSignature(compiled[field]);
            }
        }

        compiled.from 	= this.INFO_SW_BOX;
        compiled.to 	= this.formatDestinationAddress(receiver);

        await (EmailService.sendEmail(compiled, { wrap: false }));

        return {
            email: receiver.email,
            isTestDataUsed: foundTestData,
        };
	}

	__createManualTemplate (tr, eventID, eventOwnerID, receiverGroup, settings) {
        let query = knex('email_template AS et')
            .insert({
                email_html: settings.template.html,
                email_subject: settings.template.subject,
                email_text: settings.template.text,
                event_owner_id: eventOwnerID,
                recipient_type: receiverGroup,
                email_template_group: AEMService.MANUAL_GROUP,
                email_template_type: AEMService.MANUAL_TYPE,
                title: 'Manually created template',
                event_id: eventID,
                is_valid: true,
                published: true
            })
            .returning('email_template_id');

        return tr.query(query).then(result => result && result.rows[0] && result.rows[0].email_template_id);
    }

    __getReceiversGroups (receiverGroup, receiverFiltersData) {
        let receiversGroups = [];

        if(receiverGroup === AEMService.CLUB_GROUP) {
            let receiversType = receiverFiltersData.receivers_type;

            if(!receiversType || receiversType === SQLQueryBuilder.clubs.CLUB_DIRECTOR_RECEIVER_TYPE) {
                receiversGroups.push({ group: AEMService.CLUB_DIRECTORS_GROUP });
            }

            if(!receiversType || receiversType === SQLQueryBuilder.clubs.STAFF_RECEIVER_TYPE) {
                receiversGroups.push({ group: AEMService.CLUB_STAFF_GROUP });
            }

            delete receiverFiltersData.receivers_type;
        } else {
            receiversGroups.push({ group: receiverGroup });
        }

        return receiversGroups;
    }

    __formatEmailsJobData (receiverFiltersData, settings) {
        return {
            filters : receiverFiltersData,
            settings: {
                not_notified             : settings.not_notified || null,
                event_registration_method: settings.regMethod,
                cc                       : settings.template.cc || null,
                bcc                      : settings.template.bcc || null,
                replyto                  : settings.template.replyto || null,
                emailCategory            : settings.emailCategory || null,
            }
        }
    }
}

/**
 * @param {Array} receivers
 *
 * @return {void}
 */
function formatBasicTickets(receivers) {
    receivers.forEach(receiver => {
        if (receiver.basic_tickets_names_list) {
            receiver.basic_tickets_names_list = receiver.basic_tickets_names_list
                .replace(/\s\(.*?\)/g, '')
                .replace('Payment created: ', '')
                .split('; ')
                .filter(s => s)
                .join(', ');
        }
    })
}

module.exports = new AEMSenderService();
