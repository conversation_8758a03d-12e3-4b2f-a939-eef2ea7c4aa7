// TODO: Make a global listener

'use strict';

var EventEmitter    = require('eventemitter2').EventEmitter2,
    util            = require('util'),
    moment          = require('moment'),
    swUtils         = require('../lib/swUtils');

function ErrorSender () {
    EventEmitter.call(this);
    this.events_details = {
        'doubles.payment.error': {
            text_format: 'An error occured while registering a doubles team. Cause: {0} {1}',
            subject: 'Doubles Payment error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'teams.payment.notification.error': {
            text_format: 'An error occured while sending notification on payment. Cause: {0} {1}',
            subject: 'Teams Payment Notification error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'tickets.payment.error': {
            text_format: 'An error occured on tickets purchase. Cause: {0} {1}',
            subject: 'Tickets Payment error',
            receivers: '<EMAIL>'
        },
        'tickets.receipt-sending.email.error': {
            text_format: 'An error occured while sending receipt email. Details: {0} {1}',
            subject: 'Tickets: Send receipt error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'tickets.receipt-sending.text.error': {
            text_format: 'An error occured while sending receipt text message. Details: {0} {1}',
            subject: 'Tickets: Send receipt error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'stripe.connect.error': {
            text_format: 'Stripe connect error. Details: {0} {1}',
            subject: 'Stripe Connect Error',
            receivers: '<EMAIL>'
        },
        'cron.tickets.error': {
            text_format: 'Tickets Cron (Set current price) Error. Cause: {0} {1}',
            subject: 'Tickets Cron Error',
            receivers: '<EMAIL>'
        },
        'cron.roster-deadline-notification.error': {
            text_format: 'Roster Deadline Notification Cron Error. Cause: {0} {1}',
            subject: 'Roster Deadline Notification Cron Error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'cron.available-transfers.error': {
            text_format: '',
            subject: 'Stripe Transfers Cron Error',
            receivers: '<EMAIL>'
        },
        'event.created': {
            text_format: 'New Event Created. Details: {0} {1}',
            subject: 'New Event Created',
            receivers: '"sw debug" <<EMAIL>>, <EMAIL>, <EMAIL>'
        },
        'doubles.divisions.empty': {
            text_format: 'Doubles Registration. An empty division list. Details: {0} {1}',
            subject: 'Doubles Registration',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'doubles.validation.error': {
            text_format: 'Athlete: {first} {last}, {usav}. Reason: {reason}'
        },
        'doubles.validation.success': {
            text_format: 'Athlete: {first} {last}, {usav}'
        },
        'doubles.team.validation.error': {
            text_format: 'Team validation failed. Reason: {reason}. ' + 
                            'Athletes: {first1} {last1}, {usav1}; {first2} {last2}, {usav2}'
        },
        'doubles.cron.error': {
            text_format: 'Doubles cron (send messages about team changes) error occured. Reason: {0} {1}',
            subject: 'Doubles Notifications',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'geo.maps-api.no-zip': {
            text_format: 'There is no location for specified data at Google API Geocoding: {0} {1}',
            subject: 'Zip location not found',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'webhook.dispute.changes': {
            text_format: 'Webhook dispute event occured. Details {0} {1}',
            subject: 'Webhook: Dispute event occured',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'webhook.error' : {
            text_format: 'Webhook error occured. Details: {0} {1}',
            subject: 'Webhook error occured',
            receivers: '"sw debug" <<EMAIL>>, "Eugene Taranov" <<EMAIL>>'
        },
        'webhook.charge.invalid-amount': {
            text_format: `Invalid purchase row amount (Charge's amount differs from purchase's amount). Details: {0} {1}`,
            subject: 'Webhook: Purchase Amount deffers from Charge Amount',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'webhook.transfer.no-event-id': {
            text_format     : 'Transfer without Event ID. Details: {0} {1}',
            subject         : 'Webhook. Transfer without "event_id"',
            receivers       : '"sw debug" <<EMAIL>>'
        },
        'swt.api.error': {
            text_format: 'SWT API Error Occured. Details: {0} {1}',
            subject: 'SWT Api Error',
            receivers: '"sw debug" <<EMAIL>>'
            // receivers: '"Eugene Taranov" <<EMAIL>>'

        },
        'login.error.multiple-user-rows': {
            text_format: 'Multiple user rows: {0} {1}',
            subject: 'Multiple user rows (on login)',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'webpoint.api-import.error': {
            text_format: 'Webpoint API IMPORT Error occured: {0} {1}',
            subject: 'Webpoint Import Error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'webpoint.api-import.invalid-usav': {
            text_format     : 'Got Invalid USAV Number during API Import: {0}',
            subject         : 'Invalid USAV Number',
            receivers       : '"sw debug" <<EMAIL>>'
        },
        'default.error': {
            text_format: 'Error occured: {0} {1}',
            subject: 'SW Error',
            receivers: '"sw debug" <<EMAIL>>'
        },
        'ban.purchase-attempt': {
            text_format : 'Db Row: {0} {1}',
            subject     : 'Banned user purchase attempt',
            receivers   : '"sw debug" <<EMAIL>>',
            cc          : '"SportWrench" <<EMAIL>>',
        },
        'purchase.team-price-changed': {
            text_format : `Following teams were moved to another division, 
                            with price differing from the initial purchase price: {0}`,
            subject     : 'Team Price Change on paid ACH Payment',
            receivers   : '"sw debug" <<EMAIL>>'
        },
        'email.sending-error': {
            text_format : 'Email sending crashed with error: {0}',
            subject     : 'Email Sending Error',
            receivers   : '"sw debug" <<EMAIL>>'
        },
        'uncollected-fee.payment.failed-saving-after-confirm': {
            text_format : 'Uncollected Fee Payment Failed to save to DB after confirmation: {0}',
            subject     : 'Uncollected Fee Payment Saving Failure',
            receivers   : '"sw debug" <<EMAIL>>'
        },
    }
    this.fromEmail = 'SportWrench <<EMAIL>>';
}

util.inherits(ErrorSender, EventEmitter);

ErrorSender.prototype.uncollectedFeePaymentFailedSavingAfterConfirm = function (err) {
    this.emit('uncollected-fee.payment.failed-saving-after-confirm', err);
}

ErrorSender.prototype.emailSendingError = function (err) {
    this.emit('email.sending-error', err);
}

ErrorSender.prototype.paidPriceChanged = function (teamsList) {
    this.emit('purchase.team-price-changed', teamsList);
}

ErrorSender.prototype.banAccessAttempt = function (error) {
    this.emit('ban.purchase-attempt', error)
}

ErrorSender.prototype.defaultError = function (error) {
    this.emit('default.error', error)
}

ErrorSender.prototype.errorOccured = function (type, error) {
    this.emit(type, error);
}

ErrorSender.prototype.webpointAPIError = function (data) {
    this.emit('webpoint.api-import.error', data);
}

ErrorSender.prototype.invalidUSAVNumber = function (data) {
    this.emit('webpoint.api-import.invalid-usav', data);
}

ErrorSender.prototype.multipleUserRows = function (users) {   
    this.emit('login.error.multiple-user-rows', users);
}

ErrorSender.prototype.sportEngineAPIError = function (data) {
    this.emit('sportengine.api-import.error', data);
}

ErrorSender.prototype.aauAPIError = function (data) {
    this.emit('aau.api-import.error', data);
}

ErrorSender.prototype.swtError = function (error, data) {    
    this.emit('swt.api.error', error, data);    
}

ErrorSender.prototype.webhookWrongChargeAmount = function (data) {
    this.emit('webhook.charge.invalid-amount', data)
}

ErrorSender.prototype.transferWOEventID = function (data) {
    this.emit('webhook.transfer.no-event-id', data)
};

ErrorSender.prototype.webhookDispute = function (data) {
    this.emit('webhook.dispute.changes', data);
}

ErrorSender.prototype.webhookError = function (data) {
    this.emit('webhook.error', data);
}

ErrorSender.prototype.ticketsReceiptEmailError = function (error) {
    this.emit('tickets.receipt-sending.email.error', error);
}

ErrorSender.prototype.ticketsReceiptMessageError = function (error) {
    this.emit('tickets.receipt-sending.text.error', error);
}

ErrorSender.prototype.noZip = function (error) {
    this.emit('geo.maps-api.no-zip', error);
}

ErrorSender.prototype.doublesCronError = function (error) {
    this.emit('doubles.cron.error', error);
}

ErrorSender.prototype.doublesTeamValidationError = function (data) {
    this.emit('doubles.team.validation.error', data);
}

ErrorSender.prototype.doublesValidationError = function (data) {
    this.emit('doubles.validation.error', data);
}

ErrorSender.prototype.doublesValidationSuccess = function (data) {
    this.emit('doubles.validation.success', data);
}

ErrorSender.prototype.doublesSuccessPayment = function (data) {
    this.emit('doubles.payment.success', data);
}

ErrorSender.prototype.doublesRegEmptyDivisionsList = function (error) {
    this.emit('doubles.divisions.empty', error);
}

ErrorSender.prototype.doublesPaymentError = function (error) {
    this.emit('doubles.payment.error', error);
}

ErrorSender.prototype.stripeConnectError = function (error) {
    this.emit('stripe.connect.error', error);
}

ErrorSender.prototype.ticketsCronError = function (error) {
    this.emit('cron.tickets.error', error);
}

ErrorSender.prototype.transfersCronError = function (error) {
    this.emit('cron.available-transfers.error', error);
}

ErrorSender.prototype.eventCreated = function (data) {
    this.emit('event.created', data);
}

ErrorSender.prototype.rosterDeadlineNotificationError = function (error) {
    this.emit('cron.roster-deadline-notification.error', error)
}

ErrorSender.prototype.teamsPaymentNotificationError = function (error) {
    this.emit('teams.payment.notification.error', error)
}

ErrorSender.prototype.ticketsPaymentError = function (error) {
    const { error: err } = error;
    if(!err) {
        throw new Error('error property is required');
    }
    this.emit('tickets.payment.error', error)
}

ErrorSender.prototype.sendMessage = function (type, error) {
    if(!(error && type)) return;

    let html, text, now = `At: ${moment().format('MMMM Do YYYY HH:mm:ss.SSSS')}`;

    if(error instanceof Error) {
        text = `Message: ${error.message}, Stack: ${error.stack}`
    } else if (typeof error === 'object') {

        let _errorStr = swUtils.stringifyObjContainingError(error, 4);
        
        html = `<pre>${_errorStr}</pre>`;
        text = text || _errorStr;
    } else {
        text = text || error;
    }

    if(!text) return;

    let d = this.events_details[type];

    if(_.isEmpty(d)) {
        loggers.errors_log.error('Unregistered event occured');
        return;
    }

    _sendMessage({
        from: this.fromEmail,
        to: d.receivers,
        cc: d.cc,
        subject: d.subject,
        text: d.text_format.format(text, now),
        html: (!!html)?d.text_format.format(html, `<div>${now}</div>`):null,
    })
}

ErrorSender.prototype.getDetails = function (type) {
    return this.events_details[type];
}

var sender = new ErrorSender();

sender.on('uncollected-fee.payment.failed-saving-after-confirm', function (err) {
    this.sendMessage(this.event, err);
});

sender.on('email.sending-error', function (err) {
    this.sendMessage(this.event, err);
});

sender.on('default.error', function (err) {
    this.sendMessage(this.event, err);
});

sender.on('purchase.team-price-changed', function (err) {
    this.sendMessage(this.event, err);
})

sender.on('webpoint.api-import.error', function (error) {
    this.sendMessage(this.event, error);
})

sender.on('webpoint.api-import.invalid-usav', function (error) {
    this.sendMessage(this.event, error);
})

sender.on('login.error.multiple-user-rows', function (users) {
    var omittedUsers = _.map(users, function (u) {
        return _.omit(u, 'pwd_salt', 'pwd_hash', 'remember_me');
    });
    this.sendMessage(this.event, omittedUsers)
})

sender.on('swt.api.error', function (error, data) {
    this.sendMessage(this.event, { error, data });
    var sql = _getLogSQL(data.url, data.method, data.ip, data.userAgent, error);
    if(!sql) return;
    Db.query(sql).catch(err => {
        loggers.errors_log.error(err);
    });
})

sender.on('sportengine.api-import.error', function (event, error) {
    this.sendMessage(this.event, error)
})

sender.on('aau.api-import.error', function (event, error) {
    this.sendMessage(this.event, error)
})

sender.on('webhook.charge.invalid-amount', function (event, error) {
    this.sendMessage(this.event, error)
})

sender.on('webhook.transfer.no-event-id', function (event, error) {
    this.sendMessage(this.event, error);
})

sender.on('webhook.dispute.changes', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('webhook.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('tickets.receipt-sending.email.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('tickets.receipt-sending.text.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('geo.maps-api.no-zip', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('doubles.cron.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('ban.purchase-attempt', function (error) {
    this.sendMessage(this.event, error);
});

sender.on('doubles.team.validation.error', function (data) {
    if(_.isEmpty(data)) return;
    var comments = this.events_details[this.event].text_format.format({
        first1: data.first1,
        last1: data.last1,
        usav1: data.usav1 || 'N/A',
        first2: data.first2,
        last2: data.last2,
        usav2: data.usav2 || 'N/A',
        reason: data.reason
    });
    var query = squel.insert().into('event_change')
                    .setFields({
                        event_id: data.event_id,
                        action: this.event,
                        comments: comments
                    });
    Db.query(query).catch(function (err) {
        loggers.errors_log.error('MessageSender error. Action:', this.event, err);
    });
});

sender.on('doubles.validation.error', function (data) {
    if(_.isEmpty(data)) return;
    var comments = this.events_details[this.event].text_format.format({
        first: data.first,
        last: data.last,
        usav: data.usav || 'N/A',
        reason: data.reason
    });
    var query = squel.insert().into('event_change')
                    .setFields({
                        event_id: data.event_id,
                        action: this.event,
                        comments: comments
                    });
    Db.query(query).catch(function (err) {
        loggers.errors_log.error('MessageSender error. Action:', this.event, err);
    });
});

sender.on('doubles.validation.success', function (data) {
    if(_.isEmpty(data)) return;
    var comments = this.events_details[this.event].text_format.format({
        usav: data.usav || 'N/A',
        first: data.first,
        last: data.last
    });
    var query = squel.insert().into('event_change')
                    .setFields({
                        event_id: data.event_id,
                        action: this.event,
                        comments: comments
                    });
    Db.query(query).catch(function (err) {
        loggers.errors_log.error('MessageSender error. Action:', this.event, err);
    });
});

sender.on('doubles.payment.success', function (data) {
    eventNotifications.add_notification(data.event_id, {                 
        roster_team_id: data.roster_team_id,
        division_id: data.division_id,
        purchase_id: data.purchase_id,
        action: 'team.entered'
    });
})

sender.on('doubles.divisions.empty', function (error) {
    this.sendMessage(this.event, error);
});

sender.on('doubles.payment.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('doubles.payment.error', function (error) {
    if(_.isEmpty(error.data)) return;
    var comments = 'Team: "{team}". Athletes USAV codes "{usav1}", "{usav2}". Reason: {text}'
                    .format({
                        team: error.data.team_name,
                        usav1: error.data.usav1 || 'N/A',
                        usav2: error.data.usav2 || 'N/A',
                        text: error.data.reason
                    });
    var query = squel.insert().into('event_change')
                .setFields({
                    event_id: error.data.event_id,
                    division_id: error.data.division_id,
                    comments: comments,
                    action: 'doubles.payment.error'
                });
    Db.query(query).catch(function (err) {
        loggers.errors_log.error('MessageSender error. Action:', this.event, err);
    });
});

sender.on('tickets.payment.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('stripe.connect.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('cron.tickets.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('cron.roster-deadline-notification.error', function (error) {
    this.sendMessage(this.event, error)
});

sender.on('cron.available-transfers.error', function (error) {
    this.sendMessage(this.event, error);
});

sender.on('event.created', function (data) {
    this.sendMessage(this.event, data)
});

sender.on('teams.payment.notification.error', function (error) {
    this.sendMessage(this.event, error);
});

function _getLogSQL (url, verb, ip, userAgent, data) {
    try {
        return (
            `INSERT INTO "public"."request_log" (
                "type", "request_url", "verb", "remote_ip", "user_agent", "data"
             ) VALUES (
                'swt_api_error', '${url}', '${verb}', '${ip}', '${userAgent}',
                ${
                    _.isObject(data)
                        ? `'${JSON.stringify(data, Object.getOwnPropertyNames(data))}'`
                        : null
                 }
             )`
        );
    } catch (e) {
        loggers.errors_log.error(e);
        return null;
    }    
}


/**
 * @param {Object} param
 * @param {String} param.from
 * @param {String} param.to
 * @param {String} param.cc
 * @param {String} param.subject
 * @param {String} param.text
 * @param {String} param.html
 */
function _sendMessage({ from, to, subject, text, html, cc = null }) {
    EmailService.sendEmail({
        from        : from,
        to          : to,
        cc          : cc,
        subject     : subject,     
        text        : text,
        html        : html
    }, { wrap: true })
        .catch(err => loggers.errors_log.error(err));
}

module.exports = sender;
