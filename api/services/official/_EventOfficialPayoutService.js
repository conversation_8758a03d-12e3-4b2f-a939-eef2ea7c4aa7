const EventOfficialRateService = require('./_EventOfficialRateService');
const swUtils = require('../../lib/swUtils');
const moment = require("moment-timezone");
const xlsx = require("xlsx");
const path = require("path");
const xlsxStyle = require("xlsx-style");
const UPLOADS_DIR = path.resolve(__dirname, '..', '..', '..', '..', 'uploads');
const {
    PAYMENT_OPTION_LABEL: OFFICIAL_PAYMENT_OPTION_LABEL,
    PAYMENT_OPTION: OFFICIAL_PAYMENT_OPTION
} = require('../../constants/event-official');

const MEMBER_TYPE = {
    STAFF: 'staff',
    OFFICIAL: 'officials'
}

class EventOfficialPayoutService {
    constructor() {
        this.WORK_STATUS = WORK_STATUS;
    }

    get MEMBER_TYPE() {
        return MEMBER_TYPE;
    }

    validateMemberType(type) {
        if(!Object.values(this.MEMBER_TYPE).includes(type)) {
            throw { validation: 'Member type is not valid' };
        }
    }

    async getEventInfo(eventID) {
        const query = squel.select()
            .from('event', 'e')
            .field('official_payment_method')
            .field('staff_payment_method')
            .field('official_additional_role_enable')
            .where('e.event_id = ?', eventID);

        return Db.query(query).then(({ rows }) => rows[0]);
    }

    async get(eventID, type, search, officialAdditionalRoleEnable) {
        this.validateMemberType(type);

        let query = squel.select()
            .from(GET_PAYOUTS_QUERY(eventID, type, search, officialAdditionalRoleEnable), 'events')

        const payouts = await Db.query(query).then(({ rows }) => rows);

        if (!payouts.length) {
            if(type === this.MEMBER_TYPE.STAFF) {
                throw { validation: `No approved staffers found` };
            } else {
                throw { validation: 'No officials found at matches' };
            }
        }

        if(type === this.MEMBER_TYPE.OFFICIAL) {
            addEmptyMatchesToOfficialsWithoutMatches(payouts, officialAdditionalRoleEnable);
        }

        const payoutsWithTotalInfo = calculateTotalForEachOfficial(payouts, type);
        const _payouts = calculateBalance(payoutsWithTotalInfo);

        return _payouts;
    }

    getTotal(payouts, type) {
        const { matches, pay, additional, rate, paid, balance } = getSumOfTotal(payouts, type);

        let result = {
            additionalPaymentsTotal: groupTotalBy({
                payouts,
                collection: 'additional_payments',
                groupBy: 'official_additional_payment_category_id',
                computedValue: 'amount',
            }),
            additional,
            pay,
            paid,
            balance
        }

        if(type === this.MEMBER_TYPE.OFFICIAL) {
            result.matchesTypeTotal = groupTotalBy({
                payouts,
                collection: 'official_matches',
                groupBy: 'type',
                computedValue: 'qty',
            });
            result.rate = rate;
            result.matches = matches;
        }

        return result;
    }

    getBaseOfficialPayoutsTotalSql(eventID, officialAdditionalRoleEnable) {
        let baseOfficialPayoutsTotalSql = knex.from('event_official AS eo')
            .leftJoin('event_official_rate AS eor', function() {
                this.on(knex.raw(`eor.rank = eo.rank`))
            })
            .innerJoin('event_official_schedule AS eos', function() {
                this.on(knex.raw(`
                    eos.event_official_id = eo.event_official_id
                    AND eos.published IS TRUE
                `))
            })
            .innerJoin('matches AS m', function() {
                this.on(knex.raw(`
                    m.display_name = eos.match_name
                    AND m.division_id = eos.division_id
                    AND m.type = eor.match_type
                `))
            })
            .innerJoin('event AS e', function() {
                this.on(knex.raw(`e.event_id = ?`, eventID));
            })
            .where('eo.event_id', eventID)
            .where('eor.event_id', eventID);
        
        if(officialAdditionalRoleEnable) {
            baseOfficialPayoutsTotalSql
                .leftJoin('event_official_additional_role AS eoar', function() {
                    this.on(knex.raw(`eoar.event_official_id = eo.event_official_id`));
                })
                .whereRaw('eor.official_additional_role_id = eoar.official_additional_role_id')
                .whereRaw('eor.official_additional_role_id IS NOT NULL');
        } else {
            baseOfficialPayoutsTotalSql.whereRaw('eor.official_additional_role_id IS NULL');
        }
        
        return baseOfficialPayoutsTotalSql;
    }

    async mark(insertData) {
        const query = knex('official_payout').insert(insertData).toString();

        return Db.query(query);
    }

    async getPayoutsInfo(eventID, eventOfficialID, memberType, officialAdditionalRoleEnable) {
        this.validateMemberType(memberType);

        return Db.query(`${GET_PAYOUTS_INFO_SQL(
            eventID, eventOfficialID, this.getBaseOfficialPayoutsTotalSql(eventID, officialAdditionalRoleEnable), memberType
        )}`)
            .then(({ rows: [row] }) => {
                if (!row) {
                    return {};
                }

                row.payment_methods = PAYMENT_METHODS;

                return row;
            });
    }

    async getArbiterpayExportData(eventID, memberType, officialAdditionalRoleEnable) {
        this.validateMemberType(memberType);

        const baseQuery = GET_PAYOUTS_QUERY(eventID, memberType, undefined, officialAdditionalRoleEnable);

        const payouts = await Db.query(`${GET_ARBITERPAY_EXPORT_SQL(eventID, memberType, baseQuery)}`)
            .then(({ rows }) => rows || []);

        if (!payouts.length) {
            throw { validation: 'No officials found at matches' };
        }

        addEmptyMatchesToOfficialsWithoutMatches(payouts, officialAdditionalRoleEnable);
        const payoutsWithTotalInfo = calculateTotalForEachOfficial(payouts, memberType);
        return calculateBalance(payoutsWithTotalInfo);
    }

    async exportExcel(eventId, member_type) {
        const event = await this.getEventInfo(eventId);

        if (!event) {
            throw { validation: 'Event not found' }
        }

        const officialAdditionalRoleEnable = event.official_additional_role_enable;
        const exportData = await this.getArbiterpayExportData(eventId, member_type, officialAdditionalRoleEnable);
        const total = this.getTotal(exportData, member_type);

        const additionalCategories = await OfficialsService.additionalPayment.getCategories(member_type);
        const categories = additionalCategories.map(({ category }) => category);

        const [eventOfficial] = exportData;
        const matchTypes = eventOfficial.official_matches.map(({ type }) => type);
        const filePath = UPLOADS_DIR + `/official_payments_exp_${Date.now()}.xlsx`;

        const workbook = xlsx.utils.book_new();
        const rows = [];

        rows.push(['Event Name', eventOfficial.name]);
        rows.push(['Event Date Start', eventOfficial.date_start]);
        rows.push(['Event Date End', eventOfficial.date_end]);
        rows.push(['']);

        if(member_type === MEMBER_TYPE.OFFICIAL) {
            rows.push(['', '', '', 'Matches', '', ...matchTypes.reduce((acc) => [...acc, "", ""], []), 'Other']);
            rows.push(['', '', '', ...matchTypes.reduce((acc, matchType) => [...acc, matchType, ""], []), 'Totals']);
        } else if(member_type === MEMBER_TYPE.STAFF) {
            rows.push(['', '', 'Other']);
            rows.push(['']);
        }

        const headers = getExcelHeader(member_type, categories, matchTypes);
        rows.push(headers);

        exportData.forEach(({ first, last, rank, official_matches, totalMatches, totalRate, additional_payments, totalPay, payouts, balance}) => {
            const additionalPayments =
                additional_payments.reduce((acc, {amount}) => [...acc, OfficialsService.payout.formatAmount(amount)], []);
            const payoutsCell = payouts.reduce((acc, {amount, payment_method, check_number, date_paid}) => {
                const datePaid = moment(date_paid).format('MM/DD/YYYY');
                const formattedAmount = OfficialsService.payout.formatAmount(amount);
                const formattedPaymentMethod = OfficialsService.payout.formatPaymentMethod(payment_method);

                return [...acc, `(${formattedAmount}, ${formattedPaymentMethod} ${check_number ? 'Check' : ''}, ${datePaid})`];
            }, []);
            const formattedTotalPay = OfficialsService.payout.formatAmount(totalPay);
            const formattedBalance = OfficialsService.payout.formatAmount(balance);
            const row = [last, first];

            if(member_type === this.MEMBER_TYPE.OFFICIAL) {
                const officialMatches = official_matches.reduce((acc, {qty, rate}) => [...acc, qty, rate], []);

                row.push(rank, ...officialMatches, totalMatches, totalRate);
            }

            row.push(...additionalPayments, formattedTotalPay, payoutsCell.join(' '), formattedBalance);
            rows.push(row);
        });

        const totals = getExcelTotals(member_type, total);
        rows.push(totals);

        const worksheet = xlsx.utils.aoa_to_sheet(rows);
        const bold = {font: {bold: true}};

        worksheet["A1"].s = bold;
        worksheet["A2"].s = bold;
        worksheet["A3"].s = bold;

        //headers bold
        const headersRowNumberStart = 4;
        const headersRowNumberEnd = 7;
        for(let row = headersRowNumberStart; row < headersRowNumberEnd; row++) {
            for(let cell = 0; cell < headers.length; cell++) {
                const cellStyle = worksheet[xlsx.utils.encode_cell({r: row, c: cell})];
                if(cellStyle) {
                    cellStyle.s = bold;
                }
            }
        }

        //totals bold
        const totalsRowNumber = headersRowNumberEnd + exportData.length;
        for(let cell = 0; cell < headers.length; cell++) {
            const cellStyle = worksheet[xlsx.utils.encode_cell({r: totalsRowNumber, c: cell})];
            if(cellStyle) {
                cellStyle.s = bold;
            }
        }

        worksheet['!cols'] = headers.map(header => ({ wch: Math.max(header?.toString().length, 10) }))

        xlsx.utils.book_append_sheet(workbook, worksheet, swUtils.getSafeFileName(eventOfficial.name));
        xlsxStyle.writeFileSync(workbook, filePath);

        return filePath;
    }

    formatAmount(amount) {
        if (!amount) {
            return '0';
        } else if (amount < 0) {
            return `-$${Math.abs(amount)}`
        } else {
            return `$${amount}`;
        }
    }

    formatPaymentMethod(paymentMethod) {
        return OFFICIAL_PAYMENT_OPTION_LABEL[paymentMethod].label;
    }
}

const WORK_STATUS = {
    APPROVED: 'approved',
};

const MATCH_TYPES_ORDER = EventOfficialRateService.GET_MATCH_TYPES_ORDER('eor.match_type');
const RANKS_ORDER = EventOfficialRateService.GET_RANKS_ORDER('eo.rank');
const PAYMENT_METHODS = [
    {
        id: OFFICIAL_PAYMENT_OPTION.ON_SITE,
        label: OFFICIAL_PAYMENT_OPTION_LABEL[OFFICIAL_PAYMENT_OPTION.ON_SITE].label,
    },
    {
        id: OFFICIAL_PAYMENT_OPTION.MAILED,
        label: OFFICIAL_PAYMENT_OPTION_LABEL[OFFICIAL_PAYMENT_OPTION.MAILED].label,
    },
    {
        id: OFFICIAL_PAYMENT_OPTION.DIRECT_DEPOSIT,
        label: OFFICIAL_PAYMENT_OPTION_LABEL[OFFICIAL_PAYMENT_OPTION.DIRECT_DEPOSIT].label,
    },
    {
        id: OFFICIAL_PAYMENT_OPTION.ARBITER_PAY,
        label: OFFICIAL_PAYMENT_OPTION_LABEL[OFFICIAL_PAYMENT_OPTION.ARBITER_PAY].label,
    },
    {
        id: OFFICIAL_PAYMENT_OPTION.NO_PAYMENT_REQUIRED,
        label: OFFICIAL_PAYMENT_OPTION_LABEL[OFFICIAL_PAYMENT_OPTION.NO_PAYMENT_REQUIRED].label,
    }
];

function GET_PAYOUTS_QUERY(eventID, type, search, officialAdditionalRoleEnable) {
    let additionalPaymentsSubquery = squel.select()
        .field('c.official_additional_payment_category_id')
        .field('COALESCE(ap.amount, 0)', 'amount')
        .from('official_additional_payment_category', 'c')
        .left_join('event_official_additional_payment', 'ap',
            squel.expr()
                .and('ap.event_id = ?', eventID)
                .and('ap.official_additional_payment_category_id = c.official_additional_payment_category_id')
                .and('ap.event_official_id = eo.event_official_id')
                .and('ap.member_type = ?', type)
        )
        .order('c.official_additional_payment_category_id')

    let query = squel.select()
        .from('event_official', 'eo')
        .field('u.first')
        .field('u.last')
        .field('eo.event_official_id')
        .field(
            squel.select()
                .field(`COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(p))), '[]'::JSON)`)
                .from(
                    squel.select()
                        .field('op.amount')
                        .field('op.payment_method')
                        .field('op.member_type')
                        .field(`op.date_paid`)
                        .field('op.check_number')
                        .from('official_payout', 'op')
                        .where('op.event_official_id = eo.event_official_id')
                        .where('op.member_type = ?', type)
                        .order('date_paid')
                , 'p')
        , 'payouts')
        .join('official', 'o', 'o.official_id = eo.official_id')
        .join('user', 'u', 'u.user_id = o.user_id')
        .left_join('event_official_additional_role', 'eoar', 'eoar.event_official_id = eo.event_official_id')
        .left_join('official_additional_role', 'oar', 'oar.official_additional_role_id = eoar.official_additional_role_id')
        .where('eo.event_id = ?', eventID);
    
    if(search) {
        query.where(
            squel.expr({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
                .and('u.first ILIKE ?', `%${search}%`)
                .or('u.last ILIKE ?', `%${search}%`)
        );
    }

    if(type === MEMBER_TYPE.OFFICIAL) {
        const officialSubQuery = squel.select()
            .field('eor.match_type', 'type')
            .field('COALESCE(count(m.type), 0)', 'qty')
            .field('eor.rate', 'rate')
            .from('event_official_rate', 'eor')
            .left_join('event_official_schedule', 'eos',
                squel.expr()
                    .and('eos.event_official_id = eo.event_official_id')
                    .and('eos.published IS TRUE')
            )
            .left_join('matches', 'm',
                squel.expr()
                    .and('m.display_name = eos.match_name')
                    .and('m.division_id = eos.division_id')
                    .and('eor.match_type = m.type')
            )
            .where('eor.rank = eo.rank') // and role if ENABLED
            .where('eor.deprecated IS NOT TRUE')
            .where('eor.event_id = ?', eventID)
            .order(`${MATCH_TYPES_ORDER}`)
            .order('eor.match_type')
            .group('eor.rate, eor.rank, eor.match_type')
        
        officialAdditionalRoleEnable
            ? officialSubQuery.where('eor.official_additional_role_id IS NOT NULL')
            : officialSubQuery.where('eor.official_additional_role_id IS NULL');
        
        if(officialAdditionalRoleEnable) {
            officialSubQuery.where(`eor.official_additional_role_id = oar.official_additional_role_id`);
        }
        
        query.field(
            squel.select()
                .field('ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(m)))')
                .from(officialSubQuery, 'm')
            ,'official_matches')
            .where('eo.is_official IS TRUE')
            .where('eo.work_status = ?', WORK_STATUS.APPROVED)
            .order(`${RANKS_ORDER}`);
        
        officialAdditionalRoleEnable
            ? query.field(`concat(eo.rank, ' - ', oar.name)`, 'rank')
            : query.field('eo.rank');
        
        additionalPaymentsSubquery.where('c.show_for_officials IS TRUE');
    } else if(type === MEMBER_TYPE.STAFF) {
        query.order('eo.created')
            .where('eo.is_staff IS TRUE')
            .where('eo.staff_work_status = ?', WORK_STATUS.APPROVED);
        
        additionalPaymentsSubquery.where('c.show_for_staff IS TRUE');
    }

    query.field(squel.select()
            .field('ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(a)))')
            .from(additionalPaymentsSubquery, 'a')
        , 'additional_payments');

    return query;
}

function GET_PAYOUTS_INFO_SQL(eventID, eventOfficialID, baseQuery, memberType) {
    return knex
        .select(
            `eo.${memberType === MEMBER_TYPE.OFFICIAL ? 'payment_option' : 'staff_payment_option'} AS payment_method`,
            'eo.event_official_id',
            { official_name: knex.raw(`FORMAT('%s %s', u.first, u.last)`) },
            {
                amount: knex('event_official AS eo')
                    .select(
                        { 
                            amount:
                                knex.raw(`
                                    ${memberType === MEMBER_TYPE.OFFICIAL  ? `
                                    COALESCE((${baseQuery
                                        .select(knex.raw(`COALESCE(SUM(eor.rate), 0)`))
                                        .where('eo.event_official_id', eventOfficialID)
                                        .groupBy('eo.event_official_id')
                                    }), 0)
                                    +
                                    (${knex('event_official_additional_payment AS ap')
                                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                                        .where('event_id', eventID)
                                        .where('ap.event_official_id', eventOfficialID)
                                        .where('ap.member_type', memberType)
                                    })` :
    
                                    memberType === MEMBER_TYPE.STAFF  ? `
                                    (${knex('event_official_additional_payment AS ap')
                                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                                        .where('event_id', eventID)
                                        .where('ap.event_official_id', eventOfficialID)
                                        .where('ap.member_type', memberType)
                                    })` : ''}
                    
                                    -
                                    (${knex('official_payout AS op')
                                        .select(knex.raw(`COALESCE(sum(amount), 0)`))
                                        .where('event_official_id', eventOfficialID)
                                        .where('op.member_type', memberType)
                                    })
                            `)
                        }
                    )
                    .where('eo.event_official_id', eventOfficialID)
            },
            {
                history: knex
                    .select(knex.raw(`COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(h))), '[]'::JSON)`))
                    .from(
                        knex
                            .select(
                                'op.created',
                                'op.date_paid',
                                { by_user: knex.raw(`FORMAT('%s %s', u.first, u.last)`) },
                                'op.amount',
                                'op.payment_method',
                                'op.member_type',
                                'op.check_number',
                                'op.notes'
                            )
                            .from('official_payout AS op')
                            .innerJoin('user AS u', 'u.user_id', 'op.user_id')
                            .where({ event_official_id: eventOfficialID, member_type: memberType })
                            .orderBy('date_paid')
                            .as('h')
                    )
            },
        )
        .from('event_official AS eo')
        .innerJoin('official AS o', 'o.official_id', 'eo.official_id')
        .innerJoin('user AS u', 'u.user_id', 'o.user_id')
        .where({
            event_official_id: eventOfficialID
        })
}

function GET_ARBITERPAY_EXPORT_SQL(eventID, memberType, baseQuery) {
    let query = baseQuery
        .field('e.name')
        .field(`TO_CHAR((e.date_start::timestamptz AT TIME ZONE e.timezone), 'MM/DD/YYYY HH24:MI') "date_start"`)
        .field(`TO_CHAR((e.date_end::timestamptz AT TIME ZONE e.timezone), 'MM/DD/YYYY HH24:MI') "date_end"`)
        .join('event', 'e', `e.event_id = ${eventID}`)

    if(memberType === MEMBER_TYPE.OFFICIAL) {
        query.where('eo.work_status = ?', WORK_STATUS.APPROVED)
    } else {
        query.where('eo.staff_work_status = ?', WORK_STATUS.APPROVED)
    }

    return query.toString();
}

function calculateTotalForEachOfficial(payouts, type) {
    const _payouts = JSON.parse(JSON.stringify(payouts));

    for (let p = 0; p < _payouts.length; p++) {
        let totalMatches = 0;
        let totalAdditional = 0;
        let totalRate = 0;

        if(type === MEMBER_TYPE.OFFICIAL) {
            // calculate total for official matches
            for (let m = 0; m < _payouts[p].official_matches.length; m++) {
                totalMatches = swUtils.normalizeNumber(totalMatches + _payouts[p].official_matches[m].qty);
                totalRate = swUtils.normalizeNumber(totalRate + _payouts[p].official_matches[m].qty * _payouts[p].official_matches[m].rate);
            }
        }

        // calculate total for additional payments
        for (let a = 0; a < _payouts[p].additional_payments.length; a++) {
            // need check additional payments type
            totalAdditional = swUtils.normalizeNumber(totalAdditional + _payouts[p].additional_payments[a].amount);
        }

        _payouts[p].totalMatches = totalMatches;
        _payouts[p].totalAdditional = totalAdditional;
        _payouts[p].totalRate = totalRate;
        _payouts[p].totalPay = swUtils.normalizeNumber(totalRate + totalAdditional);
    }

    return _payouts;
}

function calculateBalance(payouts)  {
    const _payouts = JSON.parse(JSON.stringify(payouts));

    _payouts.forEach(payout => {
        const payoutsAmount = payout.payouts.reduce((acc, { amount }) => {
            return swUtils.normalizeNumber(acc + amount);
        }, 0);

        payout.balance = swUtils.normalizeNumber(payout.totalPay - payoutsAmount);
    });

    return _payouts;
}

function groupTotalBy({ payouts, collection, groupBy, computedValue }) {
    const info = {};

    for (let i = 0; i < payouts[0][collection].length; i++) {
        info[payouts[0][collection][i][groupBy]] = 0;
    }

    for (let p = 0; p < payouts.length; p++) {
        for (let c = 0; c < payouts[p][collection].length; c++) {
            info[payouts[p][collection][c][groupBy]] = swUtils.normalizeNumber(info[payouts[p][collection][c][groupBy]] + payouts[p][collection][c][computedValue]);
        }
    }

    return _.map(info, (value) => value);
}

function getSumOfTotal(payouts, type) {
    const total = {
        matches: 0,
        pay: 0,
        additional: 0,
        rate: 0,
        balance: 0,
        paid: 0,
    }

    for (let p = 0; p < payouts.length; p++) {
        if(type === MEMBER_TYPE.OFFICIAL) {
            total.matches = swUtils.normalizeNumber(total.matches + payouts[p].totalMatches);
            total.rate = swUtils.normalizeNumber(total.rate + payouts[p].totalRate);
        }

        total.pay = swUtils.normalizeNumber(total.pay + payouts[p].totalPay);
        total.additional = swUtils.normalizeNumber(total.additional + payouts[p].totalAdditional);
        
        for (let _p = 0; _p < payouts[p].payouts.length; _p++) {
            let amount = payouts[p].payouts[_p].amount;
            if(payouts[p].payouts[_p].member_type != type) amount = 0;
            total.paid = swUtils.normalizeNumber(total.paid + amount);
        }

        total.balance = swUtils.normalizeNumber(total.pay - total.paid);
    }

    return total;
}

function getDefaultMatchesTypesWithRates(payouts, officialAdditionalRoleEnable) {
    let defaultOfficialMatches = [];

    for (let i = 0; i < payouts.length; i++) {
        if (payouts[i].official_matches && payouts[i].official_matches.length) {
            for (let k = 0; k < payouts[i].official_matches.length; k++) {
                const item = Object.assign({}, payouts[i].official_matches[k]);

                item.qty = 0;
                if(officialAdditionalRoleEnable) {
                    item.rate = 0;
                }
                defaultOfficialMatches.push(item);
            }

            break;
        }
    }

    return defaultOfficialMatches;
}

function addEmptyMatchesToOfficialsWithoutMatches(payouts, officialAdditionalRoleEnable) {
    const officialMatches = getDefaultMatchesTypesWithRates(payouts, officialAdditionalRoleEnable);

    for (let i = 0; i < payouts.length; i++) {
        if (!payouts[i].official_matches) {
            payouts[i].official_matches = officialMatches;
        }
    }
}

function getExcelHeader(member_type, categories, matchTypes) {
    const headers = ['Last', 'First'];
    const categoriesHeaders = categories.reduce((acc, category) => [...acc, category], [])

    if(member_type === MEMBER_TYPE.OFFICIAL) {
        const headerMatchTypes = matchTypes.reduce((acc) => [...acc, 'Qty', 'Rate'], []);

        headers.push('Rank', ...headerMatchTypes, 'Qty', 'Fees');
    }

    headers.push(...categoriesHeaders, 'Total Fees Due', 'Payment Details', 'Balance');

    return headers;
}

function getExcelTotals(member_type, total) {
    const totalRate = OfficialsService.payout.formatAmount(total.rate);
    const additionalPaymentsTotal = total.additionalPaymentsTotal.reduce((acc, amount) =>
        [...acc, OfficialsService.payout.formatAmount(amount)], []);
    const totalPay = OfficialsService.payout.formatAmount(total.pay);
    const totalPaid = OfficialsService.payout.formatAmount(total.paid);
    const totalBalance = OfficialsService.payout.formatAmount(total.balance);
    const totals = ['Total', ''];

    if(member_type === MEMBER_TYPE.OFFICIAL) {
        const matchesTypeTotal = total.matchesTypeTotal.reduce((acc, matchesTypeTotal) => [...acc, matchesTypeTotal, ''], []);

        totals.push('', ...matchesTypeTotal, total.matches, totalRate);
    }

    totals.push(...additionalPaymentsTotal, totalPay, totalPaid, totalBalance);

    return totals;
}

module.exports = new EventOfficialPayoutService();
