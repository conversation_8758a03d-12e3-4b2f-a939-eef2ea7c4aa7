const moment = require('moment');
const {COURTS_DEFAULT_HOURS_COUNT, MATCHES_SOURCE_ACCEPTABLE_TYPE} = require('../../constants/esw');

class MatchesService {
    constructor(swUtilsService, eswUtilsService) {
        this.swUtilsService = swUtilsService;
        this.eswUtilsService = eswUtilsService;
    }

    async getStandings(poolId) {
        const standings = [];
        const rosterTeams = await this.#getRosterTeams(poolId);

        for (const rosterTeam of rosterTeams) {
            const standing = await this.#getStandingRow(poolId, rosterTeam);

            if(standing?.roster_team_id) {
                standings.push(standing);
            }
        }

        return standings;
    }

    async getCourtsMatches(params) {
        const {hour, matchesHours} = await this.#getMatchesStartTime(params);
        const [firstMatchesHour] = matchesHours;

        params.hour = hour ? hour : firstMatchesHour.time;
        params.hoursCount = params.hoursCount || COURTS_DEFAULT_HOURS_COUNT

        const [divisions, courts] = await Promise.all([
            this.#getDivisions(params),
            this.#getCourts(params)
        ]);

        return {
            hours: matchesHours,
            divisions,
            courts: this.#decorateCourts(courts)
        }
    }

    async getBracketMatches(poolBracket) {
        const {bracket_id, pb_seeds} = poolBracket;

        const matches = await this.#getBracketMatchesRows(bracket_id);

        return matches.map(match => this.#decorateMatch(match, pb_seeds));
    }

    async #getMatchesStartTime(params) {
        this.#validateParams(params);

        const {eventId, day, hour} = params;

        const nowInEventTimezone = await this.#getNowInEventTimezone(eventId, day);

        const matchesHours = await this.#getMatchesHours(eventId, day);

        return this.#decorateMatchesHours(matchesHours, hour, nowInEventTimezone);
    }

    async #getDivisions(params) {
        const {eventId, day} = params;

        if (!day) {
            return null;
        }

        const query =
            `SELECT DISTINCT d.division_id, d.name division_name, d.gender,
                d.sort_order, d.max_age, d.level_sort_order, d.level
            FROM matches m
            INNER JOIN event e ON m.event_id = e.event_id
            LEFT JOIN division d ON d.division_id = m.division_id
            WHERE e.esw_id = $1
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        const {rows} = await Db.query(query, [eventId]);

        return rows;
    }

    async #getCourts(params) {
        const {eventId, day, hour, hoursCount, divisionId} = params;

        const queryParams = [eventId, day, hour, hoursCount];
        const filterDataByDivision = !_.isNaN(divisionId) && divisionId > 0;
        if(filterDataByDivision) {
            queryParams.push(divisionId)
        }

        const query =
            `SELECT
                mt.event_id, mt.court_id, c.name court_name, c.short_name,
                (
                    SELECT array_to_json(array_agg(row_to_json(ctmatches))) 
                    FROM (
                        SELECT m.match_id, m.display_name match_name, m.source, m.results, pb.pb_seeds, 
                            d.division_id, d.name division_name, d.short_name division_short_name,
                            extract(epoch from m.secs_start)::BIGINT * 1000 date_start, 
                            extract(epoch from m.secs_end)::BIGINT * 1000 date_end, 
                            extract(epoch from m.secs_finished)::BIGINT * 1000 secs_finished, 
                            m.team1_roster_id, 
                            m.team2_roster_id, 
                            rt1.team_name team_1_name, 
                            rt2.team_name team_2_name, 
                            rtr.team_name team_ref_name,
                            d.color 
                        FROM matches m 
                        LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id 
                        LEFT JOIN division d ON d.division_id = m.division_id 
                        LEFT JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id 
                        LEFT JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id 
                        LEFT JOIN roster_team rtr ON rtr.roster_team_id = m.ref_roster_id 
                        WHERE m.event_id = mt.event_id 
                            AND m.court_id = mt.court_id
                            ${ filterDataByDivision ? 'AND m.division_id = $5' : '' }
                            AND m.secs_start >= $2::TIMESTAMP + ($3::INTEGER || ' hour')::INTERVAL
                            AND m.secs_start < $2::TIMESTAMP + (($3::INTEGER + $4::INTEGER) || ' hour')::INTERVAL
                        ORDER BY m.secs_start 
                    ) AS ctmatches 
                ) AS matches 
            FROM matches mt 
            INNER JOIN "event" e ON e.event_id = mt.event_id  
            LEFT JOIN courts c ON mt.court_id = c.uuid 
            WHERE e.esw_id = $1
                AND mt.secs_start >= $2::TIMESTAMP + ($3::INTEGER || ' hour')::INTERVAL
                AND mt.secs_start < $2::TIMESTAMP + (($3::INTEGER + $4::INTEGER) || ' hour')::INTERVAL
            GROUP BY mt.event_id, mt.court_id, c.name, c.sort_priority, c.short_name
            ORDER BY c.sort_priority `;

        const {rows: courts} = await Db.query(query, queryParams)

        return courts;
    }

    async #getRosterTeams(poolId) {
        const query =
            `SELECT DISTINCT roster_team_id FROM (
                SELECT m.team1_roster_id AS roster_team_id
                FROM matches m
                WHERE m.pool_bracket_id = $1
                UNION
                SELECT m.team2_roster_id AS roster_team_id
                FROM matches m
                WHERE m.pool_bracket_id = $1
            ) ids`;

        const {rows} = await Db.query(query, [poolId])

        return rows;
    }

    async #getStandingRow(poolId, rosterTeam) {
        const query = `
            SELECT _team_name team_name,
                _roster_team_id roster_team_id,
                _organization_code organization_code,
                _matches_won matches_won,
                _matches_lost matches_lost,
                _sets_won sets_won,
                _sets_lost sets_lost,
                _points_won points_won,
                _points_lost points_lost
            FROM (
                SELECT
                    sum(
                        CASE WHEN m.results IS NOT NULL 
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'matches_won'
                                        ELSE m.results::json->'team2'->>'matches_won'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _matches_won,
                    sum(
                        CASE WHEN m.results IS NOT NULL
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'matches_lost'
                                        ELSE m.results::json->'team2'->>'matches_lost'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _matches_lost,
                    sum(
                        CASE WHEN m.results IS NOT NULL
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'sets_won'
                                        ELSE m.results::json->'team2'->>'sets_won'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _sets_won,
                    sum(
                        CASE WHEN m.results IS NOT NULL
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'sets_lost'
                                        ELSE m.results::json->'team2'->>'sets_lost'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _sets_lost,
                    sum(
                        CASE WHEN m.results IS NOT NULL
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'points_won'
                                        ELSE m.results::json->'team2'->>'points_won'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _points_won,
                    sum(
                        CASE WHEN m.results IS NOT NULL
                            THEN (
                                CAST(
                                    CASE
                                        WHEN m.team1_roster_id = $1
                                            THEN m.results::json->'team1'->>'points_lost'
                                        ELSE m.results::json->'team2'->>'points_lost'
                                    END AS INTEGER
                                )
                            )
                            ELSE 0
                        END
                    ) as _points_lost,
                    CASE
                        WHEN m.team1_roster_id = $1
                            THEN rt1.team_name
                        ELSE rt2.team_name
                    END AS _team_name,
                    CASE
                        WHEN m.team1_roster_id = $1
                            THEN rt1.roster_team_id
                        ELSE rt2.roster_team_id
                    END AS _roster_team_id,
                    CASE
                        WHEN m.team1_roster_id = $1
                            THEN rt1.organization_code
                        ELSE rt2.organization_code
                    END AS _organization_code
                FROM "matches" m  
                LEFT JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id 
                LEFT JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id 
                WHERE (m.team1_roster_id = $1 OR m.team2_roster_id = $1) 
                    AND m.pool_bracket_id = $2 
                GROUP BY _team_name, _roster_team_id, _organization_code
            ) sq`;

        const {rows: [standing]} = await Db.query(query, [rosterTeam.roster_team_id, poolId]);

       return standing;
    }

    #validateParams(params) {
        const {day, eventId, hoursCount} = params;

        if(!moment(day, 'YYYY-MM-DD', true).isValid()) {
            throw { validation: 'Invalid Event day passed' };
        }

        if(!this.swUtilsService.isESWId(eventId)) {
            throw { validation: 'Invalid Event Identifier passed' };
        }

        if(!hoursCount) {
            throw { validation: 'Invalid Duration passed' };
        }
    }

    async #getNowInEventTimezone(eventId, day) {
        const query = `
            SELECT
                EXTRACT(HOUR FROM NOW() AT TIME ZONE e.timezone) "hour" 
            FROM "event" e 
            WHERE e.esw_id = $1 
                AND to_char((now() AT TIME ZONE e.timezone), 'YYYY-MM-DD') = $2`;

        const {rows: [now]} = await Db.query(query, [eventId, day]);

        return now;
    }

    async #getMatchesHours(eventId, day) {
        if (!day) {
            return null;
        }

        const query =
            `SELECT DISTINCT to_char(m.secs_start, 'HH24')::INT as time, 
                to_char(m.secs_start, 'FMHH12 AM') as time12 
            FROM matches m 
            INNER JOIN event e ON m.event_id = e.event_id 
            WHERE e.esw_id = $1 
                AND to_char(m.secs_start, 'YYYY-MM-DD') = $2 
            ORDER BY to_char(m.secs_start, 'HH24')::INT `;

        const {rows} = await Db.query(query, [eventId, day]);

        return rows;
    }

    #decorateMatchesHours(matchesHours, hour, nowInEventTimezone) {
        if (!hour) {
            if (_.isEmpty(nowInEventTimezone)) {
                const [firstMatchesHour] = matchesHours;

                hour = firstMatchesHour?.time;
            } else {
                hour = nowInEventTimezone.hour;
            }
        }

        matchesHours.push({
            default: hour
        });

        return {
            hour,
            matchesHours
        };
    }

    #decorateCourts(courts) {
        return courts.map((court) => {
            const {matches} = court;

            if (!_.isEmpty(matches)) {
                court.matches = this.#decorateMatches(matches);
            }

            return court;
        });
    }

    #decorateMatches(matches) {
        return matches.map((match) => {
            const {
                results,
                pb_seeds: pbSeeds,
            } = match;

            if (!_.isEmpty(results)) {
                this.#addResultsDataToMatch(results, match);
            }

            if (match.source && pbSeeds) {
                let source = [], seeds = [];

                try {
                    source = JSON.parse(match.source);

                    seeds = this.eswUtilsService.parseSeeds(pbSeeds);
                } catch (e) {
                    //
                }

                match.team_1_name = this.#getParticipantData(source, seeds, 'team1', match.team_1_name);
                match.team_2_name = this.#getParticipantData(source, seeds, 'team2', match.team_2_name);
                match.team_ref_name = this.#getParticipantData(source, seeds, 'ref', match.team_ref_name);

                delete match.results;
                delete match.source;
                delete match.pb_seeds;
            }

            return match
        });
    }

    #addResultsDataToMatch(results, match) {
        const {team1, team2, winner} = results;

            if (winner) {
                const teamWinner = results[`team${winner}`];

                if (teamWinner?.scores) {
                    match.scores = teamWinner.scores;
                }

                match.res_team1_roster_id = team1.roster_team_id;
                match.res_team2_roster_id = team2.roster_team_id;
                match.res_winner = winner;
            }
    }

    #getParticipantData(source, seeds, participantFieldName, matchParticipantName) {
        const sourceParticipant = source?.[participantFieldName];
        const participantType = sourceParticipant?.['type'];
        const participantSeed = sourceParticipant?.['seed'];

        let participantName = matchParticipantName;

        if (sourceParticipant && participantType && participantType === MATCHES_SOURCE_ACCEPTABLE_TYPE && participantSeed) {
            const newPool = seeds[participantSeed];

            if (newPool) {
                participantName = participantName || newPool.name || sourceParticipant.name;
            }
        }

        if(!participantName) {
            participantName = sourceParticipant.name
        }

        return participantName;
    }

    #setParticipantMatchData(source, seeds, participantFieldName, match) {
        const sourceParticipant = source?.[participantFieldName];
        const participantType = sourceParticipant?.['type'];
        const participantSeed = sourceParticipant?.['seed'];

        if (sourceParticipant && participantType && participantType === MATCHES_SOURCE_ACCEPTABLE_TYPE && participantSeed) {
            const newPool = seeds[participantSeed];

            if (newPool) {
                match[`${participantFieldName}_pool_id`] = newPool.id;
                match[`${participantFieldName}_pool_name`] = newPool.name;
            }
        }
    }

    async #getBracketMatchesRows(bracketId) {
        // Subquery is faster in this case than multiple left joins and then multiple left joins to CTE of everything else
        const subqueryRosterTeam = (colName, idName) =>
            `(SELECT rt.${colName} FROM roster_team rt WHERE rt.roster_team_id = ${idName} LIMIT 1)`;

        const query =
            `SELECT m.match_number, m.match_id, m.source, m.finishes,
                rt1.extra->>'show_previously_accepted_bid' show_previously_accepted_bid_team1,
                rt2.extra->>'show_previously_accepted_bid' show_previously_accepted_bid_team2,
                m.team1_roster_id, rt1.team_name team1_name, rt1.organization_code team1_code,
                m.team2_roster_id, rt2.team_name team2_name, rt2.organization_code team2_code,
                m.ref_roster_id,   rt3.team_name ref_name, rt3.organization_code ref_code,
                (
                    SELECT rank
                    FROM division_standing ds
                    WHERE ds.team_id = m.team1_roster_id
                        AND ds.event_id = m.event_id
                        AND ds.division_id = m.division_id
                    LIMIT 1
                ) team1_rank,
                (
                    SELECT rank
                    FROM division_standing ds
                    WHERE ds.team_id = m.team2_roster_id
                        AND ds.event_id = m.event_id
                        AND ds.division_id = m.division_id
                    LIMIT 1
                ) team2_rank,
                bm.team1_roster_id team1_temp_roster_id, bm.team2_roster_id team2_temp_roster_id,
                ${subqueryRosterTeam('team_name', 'bm.team1_roster_id')} team1_temp_name,
                ${subqueryRosterTeam('team_name', 'bm.team2_roster_id')} team2_temp_name,
                ${subqueryRosterTeam('team_name', 'bm.winning_roster_id')} winning_temp_name,
                bm.score temp_score, m.display_name, bm.winning_roster_id,
                extract(epoch from m.secs_start)::BIGINT * 1000 date_start,
                c.name court_name, c.uuid court_id,
                m.results, m.footnote_play, m.footnote_team1, m.footnote_team2
            FROM matches m
            LEFT JOIN bracketmatch bm ON bm.uuid = m.pool_bracket_id AND bm.match_number = m.match_number
            LEFT JOIN courts c ON c.uuid = m.court_id
            LEFT JOIN LATERAL (
                SELECT rt.team_name, rt.extra, rt.organization_code
                FROM roster_team rt
                WHERE rt.roster_team_id = m.team1_roster_id
                LIMIT 1
            ) rt1 ON TRUE
            LEFT JOIN LATERAL (
                SELECT rt.team_name, rt.extra, rt.organization_code
                FROM roster_team rt
                WHERE rt.roster_team_id = m.team2_roster_id
                LIMIT 1
            ) rt2 ON TRUE
            LEFT JOIN LATERAL (
                SELECT rt.team_name, rt.organization_code
                FROM roster_team rt
                WHERE rt.roster_team_id = m.ref_roster_id
                LIMIT 1
            ) rt3 ON TRUE
            WHERE m.pool_bracket_id = $1
            ORDER BY m.match_number`;

        const {rows} = await Db.query(query, [bracketId]);

        return rows;
    }

    #decorateMatch(match, pbSeeds) {
        const {results} = match;

        if (!_.isEmpty(results)) {
            const {winner} = results;

            if (winner) {
                const teamWinner = results[`team${winner}`];

                if (teamWinner?.scores) {
                    match.scores = teamWinner.scores;
                }

                match.winning_team_id = match[`team${winner}_roster_id`] || 0;
                match.winning_team_name = match[`team${winner}_name`] || '';
                match.winner = winner;
            }

            if (match.winning_roster_id) {
                match.winning_team_id = match.winning_roster_id;
                match.winning_team_name = match.winning_temp_name;
                delete match.winning_roster_id;
                delete match.winning_temp_name;
            }

            if (match.temp_score) {
                match.scores = match.temp_score;
                delete match.temp_score;
            }
        }

        if (match.source && pbSeeds) {
            let source = [], seeds = [];

            try {
                source = JSON.parse(match.source);

                seeds = this.eswUtilsService.parseSeeds(pbSeeds);
            } catch (e) {
                //
            }

            this.#setParticipantMatchData(source, seeds, 'team1', match);
            this.#setParticipantMatchData(source, seeds, 'team2', match);
            this.#setParticipantMatchData(source, seeds, 'ref', match);
        }

        return match;
    }
}

module.exports = MatchesService;
