'use strict';

const 
    utils       = require('../lib/QRTicketsGenerator'),
    argv        = require('optimist').argv,
    Hashids     = require('hashids'),
    assert      = require('assert'),
    moment      = require('moment'),
    hashids     = new Hashids(sails.config.swt.receiptSault);
const { IS_SW2584_EVENT } = require("../constants/hardcoded");

const
    CODE_REG_EXP        = /^([a-z]|[0-9]){32}-[0-9]{9}-([a-z]|[0-9]){32}$/i,
    URL_FORMAT          = `${sails.config.urls.swt.baseUrl}/images/qrcode/{0}.png`,
    CODE_LENGTH         = 64,
    BARCODE_LENGTH      = 9,
    DELIMETERS_LENGTH   = 2,
    SOCIAL_LINKS        = sails.config.eventSocialLinks,
    PG_CURRENCY_FORMAT  = 'FM$999,999,999,990D00',
    APP_DOMAIN          = sails.config.urls.main_app.baseUrl,
    DOMAIN_URL          = sails.config.urls.home_page.baseUrl,
    RECEIPT_URL         = DOMAIN_URL + '/tickets/receipt/',
    SHORT_RECEIPT_URL   = `${APP_DOMAIN}/r/`,
    SHORT_TICKETS_URL   = `${APP_DOMAIN}/pt/`,
    QRCODE_IMG_URL      = DOMAIN_URL + '/images/qrcode.svg',
    DEFAULT_SENDER      = 'SportWrench <<EMAIL>>';
// code example: e648b383fd7ce0996b84a77317230c87-773921616-445c2a66a1921ef964211944e497099e

module.exports = {
	retrieveData: function (barcode, code) {
		return getReceiptData(barcode, code);
	},
    shortHashData: function (shortHash) {
        let decodedIds = hashids.decode(shortHash);

        if(decodedIds.length !== 3) {
            return Promise.reject({ validation: 'Receipt Identifier is not recognized' });
        }

        let [purchaseId, eventId, userId] = decodedIds;

        if (userId === 0) {
            userId = -1;
        }

        if(!purchaseId) {
            return Promise.reject({ validation: 'Receipt Identifier has no reference to payment' });
        }

        if(!eventId) {
            return Promise.reject({ validation: 'Receipt Indentifier has no reference the event' });
        }

        if(!userId) {
            return Promise.reject({ validation: 'Receipt Indentifier has no reference to user' });
        }

        return getShortLinkData({ purchaseId, eventId, userId });
    },
    getEventDatesInfo: getEventDatesInfo,

    convertHashToBarcode: function (code) {
		if(!CODE_REG_EXP.test(code)) {
			throw new Error('Invalid receipt code');
		} 

		return code.substring(
            (CODE_LENGTH / 2) + 1, 
            (CODE_LENGTH / 2) + BARCODE_LENGTH + DELIMETERS_LENGTH - 1
        );
	},
    getShortReceiptLink: function (purchaseId, eventId, userId) {
        return generateShortReceiptLink(purchaseId, eventId, userId);
    },
    generateUniqueBarcode: function () {
        return generateTicketBarcode();
    },
    sendWailtlistCampsNotification: function (purchase) {
	    if(!purchase || !purchase.items.length) {
	        return Promise.reject(new Error('Purchase Items not found'));
        }

	    let camps = purchase.items;

	    if(camps.length) {
            return Promise.all(
                camps.map(camp => {
                    assert(purchase.event_id    , 'Event ID Required');
                    assert(purchase.purchase_id , 'Purchase ID Required');
                    assert(camp.camp_id         , 'Camp ID required');

                    return sendWaitlistNotification(purchase.event_id, camp.camp_id, purchase.purchase_id);
                })
            )
        }
    },
    sendPurchaseTicketsTextMessage (data) {
        const text = `Your tickets to ${data.eventName} here: `;
        const link = __generatePurchaseTicketsLink(data.purchase_id, data.event_id, data.user_id);

        const message = text + link;

        return this.sendText(_.extend(data, { text: message }));
    },
    sendPaymentInfo: function(eventID, paymentID) {
	    if (!eventID) {
	        return Promise.reject('Event ID required');
        }

        if (!paymentID) {
	        return Promise.reject('Payment ID required');
        }

        const group = AEMService.TICKETS_PAYMENTS_GROUP;
        const type = AEMService.TICKETS_PAYMENTS_GROUP_TYPE.ASSIGNED_TICKETS_RECEIPT;
        const receiversFilters = { purchase_id: paymentID };

        return AEMSenderService.sendTriggerNotification(group, type, eventID, receiversFilters)
            .catch(err => {
                ErrorSender.ticketsReceiptEmailError({
                    data    : { payment: paymentID, event: eventID },
                    error   : err
                });
                throw err;
            })
    },

    sendTicketNotification: function (eventID, ticketBarcode, ticketType, isAppleDevice, receiverEmail = null) {
        if(!ticketBarcode) {
            return Promise.reject('Ticket barcode required');
        }

        if(!eventID) {
            return Promise.reject('Event ID required');
        }

        if(!ticketType || !Object.values(TicketsService.EVENT_TICKET_TYPE).includes(ticketType)) {
            return Promise.reject('Ticket Type invalid');
        }

        const group = AEMService.TICKETS_PAYMENTS_GROUP;
        const type = ticketType === TicketsService.EVENT_TICKET_TYPE.DAILY
            ? AEMService.TICKETS_PAYMENTS_GROUP_TYPE.ASSIGNED_TICKETS_TICKET_DAILY
            : AEMService.TICKETS_PAYMENTS_GROUP_TYPE.ASSIGNED_TICKETS_TICKET_WEEKEND;

        const receiversFilters = { ticket_barcode: ticketBarcode, isAppleDevice, receiverEmail };

        return AEMSenderService.sendTriggerNotification(group, type, eventID, receiversFilters)
            .catch(err => {
                ErrorSender.ticketsReceiptEmailError({
                    data    : { ticket: ticketBarcode, event: eventID },
                    error   : err
                });
                throw err;
            })
    },

    sendReceipt: function (receiver, data) {
        if(!receiver) {
            return Promise.reject('Receiver required');
        }
        if(_.isEmpty(data)) {
            return Promise.reject('Empty data provided');
        }
        if(!data.hash) {
            return Promise.reject('No receipt hash provided');
        }

        let templateData;

        let subject = createEmailSubject(
            IS_SW2584_EVENT(data.event_id) ? 'spectator' : data.sales_type,
            data.event_name,
            data.require_tickets_names ? `${data.first} ${data.last}'s` : null
        );

        if(data.require_tickets_names) {
            data.allPurchases = this.formatPurchases(data.allPurchases);
            data.event_logo = `${DOMAIN_URL}${data.event_logo}`;
            data.app_domain = APP_DOMAIN;
            data.phone = formatPhoneNumber(data.phone);

            data.event_dates_info = getEventDatesInfo(data);
            delete data.event_date_start;
            delete data.event_date_end;
            delete data.city;
            delete data.state;

            subject = `${data.first} ${data.last}'s` + ' Tickets for ' + data.event_name;
        }

        try {
            templateData = _.extend(data, {
                description_text    : data.description?formatDescription(data.description):null,
                qr_url              : URL_FORMAT.format(data.hash),
                receipt_url         : RECEIPT_URL + data.hash,
                social_links        : castSocialLinks(data.social_links, SOCIAL_LINKS),
                domain_url          : DOMAIN_URL,
                app_domain          : APP_DOMAIN,
                specificLabels      : getReceiptLetterLabels(data),
                qrcode_img          : QRCODE_IMG_URL,
                apple_wallet_url    : `${DOMAIN_URL}/api/pass/${data.event_id}/${data.purchase_id}`,
                apple_wallet_icon   : APP_DOMAIN + sails.config.applePass.apple_wallet_icon,
            })
        } catch (e) {
            ErrorSender.ticketsReceiptEmailError({
                data    : data,
                email   : receiver,
                error   : e.message
            });
            return Promise.reject(e.message);
        }

        let template = data.require_tickets_names
            ? 'tickets/assigned/letter' : 'tickets/letter';


        return EmailService.renderAndSend({
            template    : template,
            data        : templateData,
            from        : DEFAULT_SENDER,
            to          : receiver,
            subject     : subject
        }).then((data) => {
            return { email: data, subject, to: receiver, from: DEFAULT_SENDER };
        }).catch(err => {
            ErrorSender.ticketsReceiptEmailError({
                data    : data,
                email   : receiver,
                error   : err
            });
            throw err;
        }) 
    },

    sendMerchandiseReceipt: async function (receiver, data) {
        if (!receiver) {
            throw new Error('Receiver required');
        }

        if (_.isEmpty(data)) {
            throw new Error('Empty data provided');
        }

        if (!data.hash) {
            throw new Error('No receipt hash provided');
        }

        const template = 'tickets/merchandise/letter';
        const subject = `Order for ${data.event_name}`;
        const templateData = _.extend(data, {
            app_domain: APP_DOMAIN,
            qr_url: URL_FORMAT.format(data.hash),
            receipt_url: RECEIPT_URL + data.hash,
            current_season: sails.config.sw_season.current
        })

        const email = await EmailService.renderAndSend({
            template: template,
            data: templateData,
            from: DEFAULT_SENDER,
            to: receiver,
            subject: subject
        });

        return {
            email,
            subject,
            to: receiver,
            from: DEFAULT_SENDER
        }
    },

    sendText: function (data) {
        return new Promise((resolve, reject) => {
            if (_.isEmpty(data)) {
                return reject(new Error('Empty data passed'));
            }

            if (!data.receiver) {
                return reject(new Error('Receiver not specified'));
            }

            let text = data.text || generateSMSText(data);

            return SmsService.sendSms(text, data.receiver, data.purchase_id, data.sender)
                .then(() => resolve())
                .catch(err => reject(err));
        });
    },
    notifyEO: function (receiver, data) {
        return EmailService.renderAndSend({
            template    : 'tickets/eo_notification',
            data        : data,
            from        : DEFAULT_SENDER, 
            to          : receiver,
            subject     : createEOEmailSubject(data.sales_type, data.event_name)
        }).catch(err => {
            loggers.errors_log.error(err);
            ErrorSender.ticketsReceiptEmailError({
                data    : data,
                email   : receiver,
                error   : err
            });
            throw err;
        })
    },
    sendTypeChangeConfirmation: function (receiver, data) {
        return EmailService.renderAndSend({
            template    : 'tickets/type-change/buyer',
            data        : data,
            from        : DEFAULT_SENDER, 
            to          : receiver,
            subject     : 'Camps payment type change confirmation'
        }).catch(err => {
            loggers.errors_log.error(err);
            ErrorSender.ticketsReceiptEmailError({
                data    : data,
                email   : receiver,
                error   : err
            });
            throw err;
        })
    },
    notifyEOAboutTypeChange: function (receiver, data) {
        return EmailService.renderAndSend({
            template    : 'tickets/type-change/eo',
            data        : data,
            from        : DEFAULT_SENDER, 
            to          : receiver,
            subject     : '[Camps] Payment type change'
        }).catch(err => {
            loggers.errors_log.error(err);
            ErrorSender.ticketsReceiptEmailError({
                data    : data,
                email   : receiver,
                error   : err
            });
            throw err;
        })
    },
    getReceiptUrl: function (receiptHash) {
        return RECEIPT_URL + receiptHash
    },
    getTypeChangeLink: function (req, eventCode, receiptHash) {
        let baseURL = getTypeChangeBaseURL(req);
        return `${baseURL}/${eventCode}/change-type/${receiptHash}`
    },
    formatPurchases: function (createdPurchases) {
        let tickets = [];

        for (let i = 0; i < createdPurchases.length; i++) {
            const item = createdPurchases[i];

            if (item.is_ticket) {
                tickets.push({
                    first: item.first,
                    last: item.last,
                    hash: `${APP_DOMAIN}/tickets/receipt/${item.hash}`
                });
            }
        }

        return tickets;
    },
    getReceiptTemplate: function(data) {
        const { require_tickets_names, use_merchandise_sales } = data;

        if (require_tickets_names) {
            return 'tickets/assigned/receipt.ejs';
        }

        if(use_merchandise_sales) {
            return 'tickets/merchandise/receipt.ejs';
        }
    
        return 'tickets/receipt.ejs';
    },

    resendTickets: async function ({ code, type, isAppleDevice, phone = null, email = null, eventID = null }) {
        let data = await this.getDataForReceiptResend({ code, eventID });

        if (_.isEmpty(data)) {
            throw {
                validation: 'No Data found'
            }
        }

        data.hash = utils.generateHash(data, true);

        if (!data.hash) {
            throw { validation: 'Hash code generation error' };
        }

        data.purchaseId = data.purchase_id;
        data.eventName = data.short_name;
        data.salesType = data.sales_type;
        data.eventId = data.event_id;
        data.userId = data.user_id;

        if(email) {
            data.email = email;
        }

        if(phone) {
            data.receiver = phone;
        }

        let sendLetter = () => {
            if (!data.email) {
                throw { validation: 'No email provided' };
            }

            return data.require_tickets_names
                ? this.sendTicketNotification(data.eventId, code, data.receipt[0].ticket_type, isAppleDevice, email)
                : this.resendBasicTickets(data, isAppleDevice);
        }

        let sendText = () => {
            if (!data.receiver) {
                throw {
                    validation: 'No phone number provided'
                }
            }

            return this.sendText(data);
        }

        return Promise.all([
            (type && type !== 'email') ? null : sendLetter(),
            (type && type !== 'phone') ? null : sendText()
        ]);
    },

    resendBasicTickets (data, isAppleDevice) {
        return this.sendReceipt(data.email, {
            event_name: data.event_name,
            barcode: data.ticket_barcode,
            description: data.tickets_receipt_descr,
            first: data.first,
            last: data.last,
            total: data.amount,
            receipt: data.receipt,
            hash: data.hash,
            additional_fields: data.additional_fields,
            additional: data.additional,
            social_links: data.social_links,
            sales_type: data.sales_type,
            payment_method: data.payment_method,
            check_details: data.check_details,
            require_tickets_names: data.require_tickets_names,
            allPurchases: data.assigned_tickets_list,
            event_id: data.event_id,
            purchase_id: data.purchase_id,
            sw_fee_payer: data.sw_fee_payer,
            stripe_fee_payer: data.stripe_fee_payer,
            service_fee: data.collected_sw_fee,
            credit_card_merchant_fee: data.stripe_fee,
            isAppleDevice,
            email: data.email,
            user_zip: data.user_zip,
            purchased_at: data.purchased_at,
            phone: data.phone,
            date_start: data.date_start,
            date_end: data.event_date_end,
            city: data.city,
            state: data.state,
            event_logo: data.event_logo,
            tickets_receipt_descr: data.tickets_receipt_descr,
            valid_dates: data.valid_dates,
            ticket_type: data.receipt[0].ticket_type,
            border_colour: data.receipt[0].border_colour
        })
    },

    getDataForReceiptResend: getDataForReceiptResend,

    async getPurchaseTicketsData (hash) {
        let decodedIds = hashids.decode(hash);

        if(decodedIds.length !== 3) {
            throw { validation: 'Receipt Identifier is not recognized' };
        }

        let [purchaseId, eventId, userId] = decodedIds;

        const query = `
            SELECT e.long_name                                "event_name",
                   e.name                                     "short_name",
                   e.event_id,
                   COALESCE(p.tickets_additional, '{}'::JSON) additional,
                   e.social_links,
                   (
                        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(at))), '[]'::JSON)
                        FROM (
                            SELECT p1.first, p1.last, p1.ticket_barcode, p1.user_id, p1.purchase_id, et.ticket_type
                            FROM purchase p1
                            LEFT JOIN "purchase_ticket" pt 
                                ON pt.purchase_id = p1.purchase_id 
                                AND pt.quantity > 0
                            INNER JOIN event_ticket et  
                                ON et.event_ticket_id = pt.event_ticket_id
                            WHERE p1.linked_purchase_id = p.purchase_id AND p1.is_ticket IS TRUE
                        ) at
                   ) "assigned_tickets",
                   e.date_start event_date_start,
                   e.date_end event_date_end,
                   e.city,
                   e.state,
                   p.type,
                   p.email,
                   (SELECT concat(em.file_path, '.', em.file_ext)
                    FROM event_media AS em
                    WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, p.event_id)
                      AND em.file_type = 'main-logo'
                    ORDER BY em.event_id DESC
                    LIMIT 1) AS                               event_logo,
                   e.tickets_receipt_descr
            FROM purchase p
                     INNER JOIN "event" e
                                ON e.event_id = p.event_id AND
                                   ("tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
            
            WHERE p.payment_for = 'tickets'
              AND p.purchase_id = $1
              AND p.event_id = $2
              AND p.user_id = $3
              AND p.type IN ('card', 'check', 'ach', 'free', 'cash')`;

        const { rows: [paymentData] } = await Db.query(query, [purchaseId, eventId, userId]);

        if(_.isEmpty(paymentData)) {
            return null;
        }

        paymentData.assigned_tickets = generateAssignedTicketsHash(paymentData.assigned_tickets, paymentData.event_id);
        paymentData.social_links = castSocialLinks(paymentData.social_links, SOCIAL_LINKS);
        paymentData.account_activation_link = `${DOMAIN_URL}/activate-account/?email=${paymentData.email}`;

        paymentData.event_dates_info = getEventDatesInfo(paymentData);
        paymentData.event_logo = `${DOMAIN_URL}${paymentData.event_logo}`;

        return paymentData;
    }
}

function getDataForReceiptResend ({ code, eventID = null }) {
    let query = `SELECT  
            p.event_id, p.ticket_barcode, p.purchase_id,
            p.user_id, p.amount, p.email, e.long_name "event_name", e.name "short_name",
            e.tickets_receipt_descr, p.first, p.last, p.phone "receiver", 
            ("tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE "require_tickets_names",
            COALESCE(
                NULLIF(e.tickets_purchase_additional_fields::TEXT, 'null')::JSONB, '[]'::JSONB
            ) "additional_fields", 
            COALESCE(p.tickets_additional, '{}'::JSON) additional, 
            e.social_links, (
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                    ELSE 'tickets'
                END
            ) "sales_type", ( 
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE THEN (
                        SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
                        FROM ( 
                            SELECT et.label , ec.name "camp_name",
                                pt.amount "price", pt.border_colour,
                                (
                                    CASE
                                      WHEN ec.date_end IS NULL
                                        THEN TO_CHAR(ec.date_start, 'Mon DD')
                                      WHEN DATE_PART('month', ec.date_end) <> DATE_PART('month', ec.date_start)
                                        THEN TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'Mon DD')
                                      ELSE TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'DD')
                                    END
                                ) "camp_dates"
                            FROM purchase_ticket pt 
                            LEFT JOIN event_ticket et 
                                ON et.event_ticket_id = pt.event_ticket_id 
                            LEFT JOIN event_camp ec
                                ON ec.event_camp_id = et.event_camp_id
                            WHERE pt.purchase_id = p.purchase_id 
                        ) t 
                    )
                    ELSE (
                        SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
                        FROM ( 
                            SELECT pt.quantity, et.label, et.ticket_type, pt.border_colour
                            FROM purchase_ticket pt 
                            LEFT JOIN event_ticket et 
                                ON et.event_ticket_id = pt.event_ticket_id 
                            WHERE pt.purchase_id = p.purchase_id 
                        ) t 
                    )
                END
            ) receipt, 
            (
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
                    FROM (
                        SELECT p1.first, p1.last, p1.user_id, p1.purchase_id, p1.ticket_barcode, e.event_id
                        FROM purchase p1 
                        WHERE p1.linked_purchase_id = p.linked_purchase_id
                            AND p1.status <> 'canceled'
                            AND p1.canceled_date IS NULL
                            AND p1.is_ticket IS TRUE
                    ) t
            ) "assigned_tickets_list",
            e.tickets_check_payment_details "check_details", 
            p.type "payment_method",
            p.collected_sw_fee,
            p.stripe_fee,
            e.tickets_sw_fee_payer "sw_fee_payer",
            e.stripe_tickets_fee_payer "stripe_fee_payer",
            p.email,
            p.zip AS user_zip,
            TO_CHAR(p.created::timestamptz AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') purchased_at,
            p.phone,
            e.date_start,
            e.date_end,
            e.city,
            e.state,
            (
                SELECT concat(em.file_path, '.', em.file_ext)
                FROM event_media AS em
                WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, p.event_id)
                AND em.file_type = 'main-logo'
                ORDER BY em.event_id DESC
                LIMIT 1 
            ) AS event_logo,
            e.tickets_receipt_descr,
            (
                SELECT
                    (SELECT ARRAY_AGG(
                        TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'Dy, Mon DD')
                    ) FROM JSONB_OBJECT_KEYS(et.valid_dates) vd)
                FROM purchase_ticket pt
                LEFT JOIN event_ticket et
                    ON et.event_ticket_id = pt.event_ticket_id
                WHERE pt.purchase_id = p.purchase_id
                    AND (e."tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
            ) "valid_dates"

        FROM purchase p 
        INNER JOIN "event" e 
            ON e.event_id = p.event_id 
        WHERE p.payment_for = 'tickets' 
            AND p.ticket_barcode = $1
            AND p.type IN ('card', 'check', 'ach', 'free', 'cash')`;

    const params = [code];

    if(eventID) {
        query += ' AND p.event_id = $2';
        params.push(eventID)
    }

    return Db.query(query, params).then(result => result?.rows?.[0]);
}

function getTypeChangeBaseURL (req) {
    let domain = req.hostname || req.headers['Host'];

    switch (domain) {
        case 'localhost':
            return `http://${domain}:3000/tickets/.tmp/public/index.html#/events`;
        default: 
            return `${sails.config.urls.swt.baseUrl}/#/events`;
    }
}

function generateSMSText (data) {
    let shortReceiptUrl = generateShortReceiptLink(data.purchaseId, data.eventId, data.userId);

    if(!shortReceiptUrl) {
        throw new Error('Invalid parameters passed. Could not generate short receipt link');
    }

    let message = shortReceiptUrl;

    if(data.useMerchandiseSales) {
        message = `Your Code to pick up your merchandise here: ` + message;
    } else if(data.eventName && data.salesType) {
        message = `Your ${(data.salesType === 'tickets')?'ticket':'receipt'} to ${data.eventName} here: ` + message;
    } else if (!data.eventName) {
        message = `Your ${(data.salesType === 'tickets')?'ticket':'receipt'} here: ` + message;
    } else if (!data.salesType) {
        message = `${data.eventName} receipt: ` + message;
    }

    return message;
}

function generateShortReceiptLink (purchaseId, eventId, userId) {
    const _userId = userId < 0 ? 0 : userId;

    let receiptHash = hashids.encode(purchaseId, eventId, _userId);
    return (!!receiptHash)?(SHORT_RECEIPT_URL + receiptHash):null;
}

function createEmailSubject (salesType, eventName, name) {
    const labelForSalesType = {
        tickets: 'Tickets ',
        spectator: 'Spectator Registration ',
    };

    let subject = `${labelForSalesType?.[salesType] ?? ''}Receipt: ${eventName}`

    return name ? `${name} ${subject}` : subject;
}

function createEOEmailSubject (salesType, eventName) {
    return `${(salesType === 'tickets')?'Tickets':'Camps'} Purchase Created for "${eventName}"`
}

function formatDescription (descr) {
    return descr.replace(/<(?:.|\s)*?>/g, '').replace(/\t/g, '')
}

function generateTicketBarcode () {
    let code = Math.floor(Math.random() * (1000000000 - 100000000 + 1)) + 100000000;
    return Db.query(
        `SELECT p.purchase_id 
         FROM purchase p 
         WHERE p.ticket_barcode = $1 
            AND p.payment_for = 'tickets'`,
        [code]
    ).then(result => {
        if(result.rows.length === 0) {
            return code;
        } else {
            return generateTicketBarcode();
        }
    });
}

function getReceiptSQL (data) {
    let params      = [],
        _whereBlock = '';

    if(data.barcode) {
        params.push(data.barcode)
        _whereBlock += `AND p.ticket_barcode = $${params.length}`;
    }

    if(data.purchaseId) {
        params.push(data.purchaseId)
        _whereBlock += 
        `
        AND p.purchase_id = $${params.length}`;
    }

    if(data.eventId) {
        params.push(data.eventId)
        _whereBlock += 
        `
        AND p.event_id = $${params.length}`;
    }

    if(data.userId) {
        params.push(data.userId)
        _whereBlock += 
        `
        AND p.user_id = $${params.length}`;
    }

    let sql = 

    `SELECT *, ( 
            CASE 
                WHEN ("receipt_data"."discounted" > 0)
                    THEN TO_CHAR("receipt_data"."discounted", '${PG_CURRENCY_FORMAT}')  
                ELSE NULL  
            END
        ) "discounted_amount", (
            ("available" = 0) AND (scanned_at IS NOT NULL)
        ) "is_scanned"
    FROM ( 
        SELECT  
            p.purchase_id, p.event_id, p.user_id, p.ticket_barcode, TO_CHAR(p.ticket_barcode, '999-999-999') AS formatted_ticket_barcode, 
            TO_CHAR(p.date_paid::timestamptz AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') date_paid,
            TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}') amount, pt.border_colour,   
            p.email, p.first, p.last, e.long_name event_name, p.phone,  
            p.zip, e.tickets_receipt_descr, e.city, e.state,  
            TO_CHAR(e.date_start, 'Mon DD, YYYY') date_start,
            (p.status = 'canceled' OR p.dispute_status = 'lost') "is_canceled_payment",
            TO_CHAR(p.date_refunded, 'Mon DD, YYYY, HH12:MI AM') date_refunded,   
            COALESCE(e.tickets_purchase_additional_fields, '[]'::JSONB) additional_fields,  
            COALESCE(p.tickets_additional, '{}'::JSON) additional, 
            e.tickets_description email_text, e.social_links, p.status, (
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE THEN 'camps' 
                    ELSE 'tickets' 
                END
             ) "sales_type", (
                     SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))), '[]'::JSON)
                     FROM (
                        SELECT
                            pt.quantity, pt.discounted_quantity, et.label, et.sort_order,
                            (pt.quantity - COALESCE(pt.discounted_quantity, 0)) "paid_qty",
                            FORMAT(
                                '%s (%s)',
                                et.label, TO_CHAR(pt.ticket_price::NUMERIC, '${PG_CURRENCY_FORMAT}')
                            ) "type_label",
                            (
                                CASE
                                  WHEN ec.date_end IS NULL
                                    THEN TO_CHAR(ec.date_start, 'Mon DD')
                                  WHEN DATE_PART('month', ec.date_end) <> DATE_PART('month', ec.date_start)
                                    THEN TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'Mon DD')
                                  ELSE TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'DD')
                                END
                            ) "camp_dates",
                            ec.name "camp_label",
                            TO_CHAR(pt.amount::NUMERIC, '${PG_CURRENCY_FORMAT}') amount,
                            TO_CHAR(pt.ticket_price::NUMERIC, '${PG_CURRENCY_FORMAT}') ticket_price,
                            pt.discount,
                            TO_CHAR(pt.discount::NUMERIC, '${PG_CURRENCY_FORMAT}') discount_amount
                            FROM "purchase_ticket" pt
                                INNER JOIN event_ticket et
                            ON et.event_ticket_id = pt.event_ticket_id
                            LEFT JOIN "event_camp" ec
                                ON ec.event_camp_id = et.event_camp_id
                                AND e.event_id = ec.event_id
                            LEFT JOIN purchase p
                                ON pt.purchase_id = p.purchase_id
                            WHERE p.payment_for = 'tickets'
                                ${_whereBlock}
                            ORDER BY et.sort_order, et.event_ticket_id
                     ) t
            ) tickets, 
            (COALESCE(SUM(pt.discount), 0) + COALESCE(p."purchase_discount", 0)) "discounted",
            SUM(pt.available) "available", (
                CASE 
                    WHEN p.scanned_at IS NOT NULL
                        THEN TO_CHAR(p.scanned_at::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM')
                    ELSE NULL
                END
            ) "scanned_at",
            (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(at))), '[]'::JSON)
                FROM (
                    SELECT p1.first, p1.last, p1.ticket_barcode, p1.user_id, p1.purchase_id, et.ticket_type
                    FROM purchase p1
                    LEFT JOIN "purchase_ticket" pt 
                        ON pt.purchase_id = p1.purchase_id 
                        AND pt.quantity > 0
                    INNER JOIN event_ticket et  
                        ON et.event_ticket_id = pt.event_ticket_id
                    WHERE p1.linked_purchase_id = p.linked_purchase_id AND p1.is_ticket IS TRUE
                ) at
            ) "assigned_tickets",
            (
            SELECT row_to_json(sub) 
                FROM (
                    SELECT 
                        ticket_wallet_link_id,
                        ticket_barcode,
                        (extract (epoch from expires_at) * 1000)::bigint AS expires_at
                    FROM ticket_wallet_link twl
                    WHERE twl.shared_by_user_id = payment.user_id
                    AND twl.ticket_barcode = p.ticket_barcode
                    AND twl.expires_at > NOW()
                    LIMIT 1
                ) AS sub
            ) AS "ticket_share_link",
            payment.user_id "purchaser_user_id",
            (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE "require_tickets_names",
            (e.tickets_settings->>'use_merchandise_sales')::BOOLEAN IS TRUE "use_merchandise_sales",
            TO_CHAR(p.collected_sw_fee::NUMERIC, '${PG_CURRENCY_FORMAT}') collected_sw_fee,
            TO_CHAR(p.stripe_fee::NUMERIC, '${PG_CURRENCY_FORMAT}') stripe_fee,
            e.tickets_sw_fee_payer "sw_fee_payer",
            e.stripe_tickets_fee_payer "stripe_fee_payer",
            p.type,
            TO_CHAR(p.received_date::timestamptz AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') received_date, 
            p.check_num,
            e.date_start AS event_date_start,
            e.date_end AS event_date_end,
            (
                SELECT concat(em.file_path, '.', em.file_ext)
                FROM event_media AS em
                WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, p.event_id)
                AND em.file_type = 'main-logo'
                ORDER BY em.event_id DESC
                LIMIT 1 
            ) AS event_logo,
            et.ticket_type,
            (SELECT (
                ARRAY_AGG(
                    TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD')
                    ORDER BY (TO_TIMESTAMP(vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')) ASC
                )
            )
            FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd) valid_dates
        FROM purchase p   
        INNER JOIN "event" e   
            ON e.event_id = p.event_id   
        LEFT JOIN "purchase_ticket" pt 
            ON pt.purchase_id = p.purchase_id 
            AND pt.quantity > 0
        JOIN purchase payment 
            ON payment.purchase_id = COALESCE(p.linked_purchase_id, p.purchase_id) 
                   AND payment.is_payment IS TRUE 
                   AND payment.event_id = p.event_id
        INNER JOIN event_ticket et  
            ON et.event_ticket_id = pt.event_ticket_id  
        LEFT JOIN "event_camp" ec 
            ON ec.event_camp_id = et.event_camp_id 
            AND e.event_id = ec.event_id 
        WHERE p.payment_for = 'tickets'   
            ${_whereBlock}
        GROUP BY p.purchase_id, payment.user_id, e.event_id, et.ticket_type, et.valid_dates, pt.border_colour
    ) "receipt_data"`;

    return { sql, params };
}

async function createTicketShareLinkRow (userID, barcode) {
    const query = knex('ticket_wallet_link')
        .insert({
            shared_by_user_id: userID,
            ticket_barcode: barcode,
            expires_at: knex.raw(`now() + interval '24 hours'`),
        })
        .returning([
            'ticket_wallet_link_id',
            'ticket_barcode',
            knex.raw(`(extract (epoch from expires_at) * 1000)::bigint AS expires_at`)
        ]);

    const { rowCount, rows: [data] = {} } = await Db.query(query, [userID, userID, barcode]);

    if(rowCount === 0) {
        throw new Error('Link not created');
    }

    return data;
}

const DEFAULT_VERSION = '1.0';

function generateShareDeepLink ({
    ticket_wallet_link_id,
    ticket_barcode,
    expires_at,
    version = DEFAULT_VERSION
}) {
   const linkContent = `${version}_${ticket_wallet_link_id}_${ticket_barcode}_${expires_at}`;

   const encodedLinkContent = Buffer.from(linkContent).toString('base64');

   return `${sails.config.swtApp.appBaseUrl}ticket/${encodedLinkContent}`
}

async function extractPaymentForDbResult (result) {
    let paymentData = _.first(result.rows);

    if(_.isEmpty(paymentData)) {
        throw {
            validation: 'Ticket not found. Please check the link.'
        }
    }
    
    if(!paymentData.ticket_share_link) {
        paymentData.ticket_share_link = await createTicketShareLinkRow(paymentData.purchaser_user_id, paymentData.ticket_barcode)
    }

    paymentData.ticket_share_deep_link = generateShareDeepLink(paymentData.ticket_share_link);
    paymentData.appStoreSwAppUrl      = sails.config.swtApp.appStoreUrl;
    paymentData.playMarketSwAppUrl      = sails.config.swtApp.playMarketUrl;
    paymentData.sw_tickets_app_icon   = APP_DOMAIN + sails.config.swtApp.swtAppIcon;
    paymentData.app_store_download_icon   = APP_DOMAIN + sails.config.swtApp.appStoreDownloadIcon;
    paymentData.play_market_download_icon   = APP_DOMAIN + sails.config.swtApp.playMarketDownloadIcon;

    paymentData.receiptHash     = utils.generateHash({
        ticket_barcode  : paymentData.ticket_barcode,
        purchase_id     : paymentData.purchase_id,
        user_id         : paymentData.user_id, 
        event_id        : paymentData.event_id
    });

    paymentData.assigned_tickets = generateAssignedTicketsHash(paymentData.assigned_tickets, paymentData.event_id);

    paymentData.url                 = URL_FORMAT.format(utils.generateImageName(paymentData.ticket_barcode, paymentData.receiptHash));
    paymentData.receipt_url         = RECEIPT_URL + paymentData.receiptHash;
    paymentData.current_season      = sails.config.sw_season.current;
    paymentData.domain_url          = DOMAIN_URL;
    paymentData.social_links        = castSocialLinks(paymentData.social_links, SOCIAL_LINKS);
    paymentData.appleWalletURL      = `${DOMAIN_URL}/api/pass/${paymentData.event_id}/${paymentData.purchase_id}`;
    paymentData.apple_wallet_icon   = APP_DOMAIN + sails.config.applePass.apple_wallet_icon;
    paymentData.formatted_phone     = formatPhoneNumber(paymentData.phone);
    paymentData.account_activation_link = `${DOMAIN_URL}/activate-account/?email=${paymentData.email}`;


    paymentData.event_dates_info = getEventDatesInfo(paymentData);
    paymentData.event_logo = `${DOMAIN_URL}${paymentData.event_logo}`;

    return paymentData;
}

function validateReceiptHash (code, receiptHash) {
    let codeHash = code.substring(0, CODE_LENGTH / 2) + code.substring((CODE_LENGTH / 2) + BARCODE_LENGTH + DELIMETERS_LENGTH);
    if(codeHash !== receiptHash) {
        throw {
            validation: 'Invalid Code'
        }
    }
}

function generateAssignedTicketsHash (tickets, eventID) {
    return tickets.map(ticket => {
        let hash = utils.generateHash({
            ticket_barcode  : ticket.ticket_barcode,
            purchase_id     : ticket.purchase_id,
            user_id         : ticket.user_id,
            event_id        : eventID
        }, true);

        ticket.url = `${APP_DOMAIN}/tickets/receipt/${hash}`;

        return ticket;
    })
}

function getReceiptData (barcode, code) {
    let queryData = getReceiptSQL({ barcode });
    return Db.query(queryData.sql, queryData.params)
    .then(extractPaymentForDbResult)
    .then(paymentData => {

        if(!!code) {
            validateReceiptHash(code, paymentData.receiptHash);
        }

        return paymentData;
    })
}

function getShortLinkData (data) {
    let queryData = getReceiptSQL(data);
    return Db.query(queryData.sql, queryData.params)
    .then(extractPaymentForDbResult);
}

function getReceiptLetterLabels (data) {
    let openLabel       = 'Receipt';
    let purchaserLabel  = 'Purchaser';
    let barcodeLabel    = 'Ticket';

    if(data.sales_type === 'tickets') {
        openLabel = 'Ticket/Receipt';

        if(data.require_tickets_names) {
            openLabel       = 'Ticket';
            purchaserLabel  = 'Ticket Holder';
        }
    }
    if(IS_SW2584_EVENT(data.event_id)) {
        openLabel = 'Spectator Registration Purchase';
        barcodeLabel = 'Spectator Registration';
    }

    return { openLabel, purchaserLabel, barcodeLabel };
}

function castSocialLinks (paymentSocialLinks, iconsLinks) {
    if(_.isEmpty(paymentSocialLinks)) {
        return [];
    } else {
        return Object.keys(paymentSocialLinks).reduce((resultLinks, key) => {
            let item    = iconsLinks[key];
            let value   = paymentSocialLinks[key]

            if (item && value) {
                resultLinks.push({
                    title   : item.name,
                    img     : item.img,
                    value   : value,
                    type    : item.type
                })
            }

            return resultLinks;
        }, []);
    }
}

function sendWaitlistNotification (eventID, campID, purchaseID) {
    let templateGroup   = AEMService.CAMP_GROUP;
    let templateType    = AEMService.triggers.CAMPS_REGISTRATION_WAITLISTED_ACTION;
    let receiverFilters = { camp: campID, payments: [purchaseID] };

    return AEMSenderService.sendTriggerNotification(templateGroup, templateType, eventID, receiverFilters);
}

function getEventDatesInfo (data) {
    const {
        event_date_start,
        event_date_end,
        city,
        state
    } = data;

    const date = getFormattedDate(event_date_start, event_date_end);
    return `${date} ${city}, ${state}`
}

function getFormattedDate(date_start, date_end) {
    const dateStart = moment(date_start);
    const dateEnd = moment(date_end);
    const year = dateStart.year();

    const isEqual = dateStart.month() === dateEnd.month();

    if (isEqual) {
        if (dateStart.date() === dateEnd.date()) {
            return `${dateStart.format('MMM D')}, ${year}`;
        }

        return `${dateStart.format('MMM D')}—${dateEnd.format('D')}, ${year}`;
    }

    return `${dateStart.format('MMM D')}—${dateEnd.format('MMM D')}, ${year}`;
}


function formatPhoneNumber(phone) {
    if(phone && phone.length >= 10) {
        phone = phone.replace(/\D/g, '')
        return phone.replace(/^(\d{3})(\d{3})(\d*)$/, '($1) $2-$3');
    }
}

function __generatePurchaseTicketsLink (purchaseID, eventID, userID) {
    let receiptHash = hashids.encode(purchaseID, eventID, userID);
    return (!!receiptHash)?(SHORT_TICKETS_URL + receiptHash):null;
}
