'use strict';

const FACEBOOK_ICON_NAME            = 'facebook';
const TWITTER_ICON_NAME             = 'twitter';
const SNAPCHAT_ICON_NAME            = 'snapchat';
const INSTAGRAM_ICON_NAME           = 'instagram';
const SOCIAL_ICONS_ACTION_NAME      = 'social_icons';
const FACEBOOK_ICON_ACTION_NAME     = 'facebook_icon';
const TWITTER_ICON_ACTION_NAME      = 'twitter_icon';
const SNAPCHAT_ICON_ACTION_NAME     = 'snapchat_icon';
const INSTAGRAM_ICON_ACTION_NAME    = 'instagram_icon';
const URL_LINK_TYPE                 = 'url';
const TEXT_LINK_TYPE                = 'text';

const DOMAIN          = sails.config.urls.home_page.baseUrl;
const SOCIAL_NETWORKS = sails.config.eventSocialLinks;

const ACTIONS = {
    [SOCIAL_ICONS_ACTION_NAME]      : __getSocialIcons__,
    [FACEBOOK_ICON_ACTION_NAME]     : __getIcon__(FACEBOOK_ICON_NAME),
    [TWITTER_ICON_ACTION_NAME]      : __getIcon__(TWITTER_ICON_NAME),
    [SNAPCHAT_ICON_ACTION_NAME]     : __getIcon__(SNAPCHAT_ICON_NAME),
    [INSTAGRAM_ICON_ACTION_NAME]    : __getIcon__(INSTAGRAM_ICON_NAME)
}

function __extendIconsData__ (icons) {
    let result = [];

    Object.keys(SOCIAL_NETWORKS).forEach(sc => {
        if(icons[sc]) {
            SOCIAL_NETWORKS[sc].value = icons[sc];

            result.push(SOCIAL_NETWORKS[sc])
        }
    });

    return result;
}

function __generateFormattedHtml__ (link) {
    let htmlUrl =
        `<a href="${link.value}" style="text-decoration:none;" target="_blank">
            <img src="${DOMAIN}${link.img}" 
                 style="width:24px;height:24px;"
                 title="${link.name}"
                 alt="${link.name}"
                >
            </a>`;

    let htmlText =
        `<img src="${link.img}" 
             style="width:24px;height 24px;"
             title="${link.name}" 
             alt="${link.name}">
         <span ng-bind="${link.value}"></span>`;

    if(link.type === URL_LINK_TYPE) {
        return `<span>${htmlUrl}</span>`;
    } else if(link.type === TEXT_LINK_TYPE){
        return `<span>${htmlText}</span>`;
    }
}

function __generateRawHtml__ (link) {
    return `${link.name}: ${link.value} `;
}

function __processHtmlGeneration__ (links) {
    let formatted   = '';
    let raw         = '';

    links.forEach(link => {
        formatted   = formatted.concat(__generateFormattedHtml__(link));
        raw         = raw.concat(__generateRawHtml__(link));
    });

    return { raw, formatted };
}

function __getSocialIcons__ (variableObj, data) {
    let social_links = __extendIconsData__(data[variableObj.field]);

    return __processHtmlGeneration__(social_links);
}

function __getIcon__ (icon) {
    return function getData(variableObj, data) {
        let linkData = { [icon]: data[variableObj.field] };

        let social_links = __extendIconsData__(linkData);

        return __processHtmlGeneration__(social_links);
    }
}

function __copyToReceiversArray__ (receivers, links) {
    receivers.forEach((receiver, id) => {
        receivers[id] = __copyToReceiver__(receiver, links)
    });

    return receivers;
}

function __copyToReceiver__ (receiver, links) {
    Object.keys(links).forEach(link => {
        receiver[link] = links[link];
    });

    return receiver;
}

function isGeneralCustomAction (action) {
    return [
        SOCIAL_ICONS_ACTION_NAME,
        FACEBOOK_ICON_ACTION_NAME,
        TWITTER_ICON_ACTION_NAME,
        SNAPCHAT_ICON_ACTION_NAME,
        INSTAGRAM_ICON_ACTION_NAME
    ].includes(action);
}


function getEventSocialLinks(eventID, receivers) {
    let query = squel.select().from('event', 'e')
        .field('e.social_links', SOCIAL_ICONS_ACTION_NAME)
        .field(`e.social_links ->> '${SNAPCHAT_ICON_NAME}'` , SNAPCHAT_ICON_ACTION_NAME)
        .field(`e.social_links ->> '${FACEBOOK_ICON_NAME}'` , FACEBOOK_ICON_ACTION_NAME)
        .field(`e.social_links ->> '${INSTAGRAM_ICON_NAME}'`, INSTAGRAM_ICON_ACTION_NAME)
        .field(`e.social_links ->> '${TWITTER_ICON_NAME}'`  , TWITTER_ICON_ACTION_NAME)
        .where('e.event_id = ?', eventID);

    return Db.query(query).then(result => result.rows[0] || null)
        .then(socialLinks => {

            if(!socialLinks) {
                return receivers;
            }
            
            if (socialLinks[SNAPCHAT_ICON_ACTION_NAME]) {
                socialLinks[SNAPCHAT_ICON_ACTION_NAME] = `https://www.snapchat.com/add/${socialLinks[SNAPCHAT_ICON_ACTION_NAME]}`;
            }

            if(Array.isArray(receivers)) {
                return __copyToReceiversArray__(receivers, socialLinks);
            } else {
                return __copyToReceiver__(receivers, socialLinks);
            }
        })
}

function generalVariablesCustomAction (variableObj, data) {
    let variableName = variableObj.field;

    let action = ACTIONS[variableName];

    if (action == null) {
        throw new Error(`Custom Action is not defined for varialbe '${variableName}'`);
    }

    return action(variableObj, data);
}


module.exports = {
    generalVariablesCustomAction: generalVariablesCustomAction,
    getEventSocialLinks         : getEventSocialLinks,
    isGeneralCustomAction       : isGeneralCustomAction
};
