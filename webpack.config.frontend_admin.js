const path = require('nde:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const glob = require('glob');

const entryFiles = glob.sync([
    './frontend_admin/sport-wrench-admin.module.js',
    './frontend_admin/**/*.js',
    './assets/styles/main.scss',
    './assets/styles/admin.scss',
], { absolute: true });

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: entryFiles,

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend_admin/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8087,
    },
};
