<fieldset ng-disabled="utils.entryChangeInProgress">
    <form class="form-inline form-indent row-space">
        <button
            ng-if="!event.past"
            class="btn btn-primary"
            ng-click="paymentMenu();"
            ng-disabled="teams.length === 0"
            >Pay</button>
        <div class="form-group">
            <label class="sr-only"></label>
            <input type="text" class="form-control" placeholder="Search" ng-model="search.value">
        </div>
        <div class="form-group">
            <label>Show:</label>
            <select
            name="show_entered"
            class="form-control"
            ng-model="search.show_teams">
                <option value="any" selected>All Teams</option>
                <option value="entered">Entered Only</option>
                <option value="not_entered">Not Entered Only</option>
            </select> 
        </div>
        <div class="form-group">
            <label for="age_from">Age:</label>
            <select name="age_from" class="form-control"
                    ng-model="search.age.from" >
                <option value="" selected>From</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13">13</option>
                <option value="14">14</option>
                <option value="15">15</option>
                <option value="16">16</option>
                <option value="17">17</option>
                <option value="18">18</option>
                <option value="19">19</option>
                <option value="20">20</option>
            </select>
            <select name="age_to" id="age_to" class="form-control"
                    ng-model="search.age.to" >
                <option value="" selected>To</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13">13</option>
                <option value="14">14</option>
                <option value="15">15</option>
                <option value="16">16</option>
                <option value="17">17</option>
                <option value="18">18</option>
                <option value="19">19</option>
                <option value="20">20</option>
            </select>
        </div>
         <div class="form-group">
              <label for="rank_from">Rank:</label>
                <select name="rank_from" class="form-control"
                        ng-model="search.rank.from" >
                    <option value="">From</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                    <option value="8">8</option>
                    <option value="9">9</option>
                </select>
                <select name="rank_to" class="form-control"
                        ng-model="search.rank.to" >
                    <option value="">To</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                    <option value="8">8</option>
                    <option value="9">9</option>
                </select> 
         </div>
         <div class="form-group">
            <label>Gender:</label>
            <label ng-if="club.has_male_teams && $parent.event.has_male_teams">
                 <genders m></genders>
                 <input type="checkbox" value="male" ng-model="genders.male">
             </label>
             &nbsp;
             <label ng-if="club.has_female_teams && $parent.event.has_female_teams">
                 <genders f></genders>
                 <input type="checkbox" value="female" ng-model="genders.female">
             </label>
             &nbsp;
             <label ng-if="club.has_coed_teams && $parent.event.has_coed_teams">
                 <genders f m></genders>
                 <input type="checkbox" value="coed" ng-model="genders.coed">
             </label>
        </div>
        <button class="btn btn-success" ng-click="printAllRosters()" ng-disabled="disablePrintBtn()">
            {{printButtonText()}}
            <spinner is-inline="true" size="1" pos="left" active="utils.printAllValidation"></spinner>
        </button>
        <button class="btn btn-info" ng-click="redirectToVIProposalPage()" ng-if="showVIProposalButton()">
            Buy Insurance
            <spinner is-inline="true" size="1" pos="left" active="utils.printAllValidation"></spinner>
        </button>
    </form>
    <uib-alert type="warning text-center">Warning: Please verify that your team code is correct before entering it into an event. You can not change anything about a team code after entering.</uib-alert>
    <uib-alert ng-if="utils.errorMessage" type="danger text-center">{{utils.errorMessage}}</uib-alert>
    <table class="table manage-teams">
        <thead>
            <tr>
                <th>Event Division</th>
                <th ng-if="$parent.eventFinished()">Results</th>
                <th title="Team Gender">G</th>
                <th></th>
                <th>Team Name</th>
                <th>USAV Code</th>
                <th>Age</th>
                <th>Rank</th>
                <th ng-if="!$parent.eventFinished()">Payment</th>
                <th ng-if="!$parent.eventFinished()">Entry</th>
                <th ng-if="!$parent.eventFinished()">Housing</th>
            </tr>
        </thead>
        <tbody>
            <tr
                ng-repeat="team in teams | clubteamsfilter:search.value:search.roster_only | orderBy:'organization_code' | filter:ageFilter() | filter:rankFilter() | filter:genderFilter() | filter:showEnteredTeamsFilter()"
                ng-class="{
                    'team-entered': (team.roster_team_id && !team.deleted && (team.status_entry !== 11)),
                    'team-deleted': team.deleted,
                    'italic': (team.status_entry === 14), 
                    'pointer': (team.roster_team_id && !team.deleted && (team.status_entry !== 11)), 
                    'bg-danger': (team.status_entry === 11)
                }"
                ng-click="showAthletes(team)"
            >
                <td class="col-sm-2" ng-click="$event.stopPropagation();">
                    <div
                        tdselectable
                        on-value-changed="divisionChange(prop, val, item, callback);"
                        prop="division_id"
                        val="team.division_id"
                        item="team"
                        dropdown="event.divisions"
                        is-shown="{{showDivisionsSelect(team)}}"
                    >
                    </div>
                </td>
                <td ng-if="$parent.eventFinished()" ng-click="$event.sportPropagation();">
                    <button
                        class="btn btn-primary btn-xs"
                        ng-click="exportResultsAES(team.roster_team_id)"
                        ng-if="team.status_entry === 12"
                    >AES</button>&nbsp;
                    <button
                        class="btn btn-primary btn-xs"
                        ng-click="exportResultsTM2(team.roster_team_id)"
                        ng-if="team.status_entry === 12"
                    >TM2</button>&nbsp;
                    <span ng-if="team.status_entry !== 12">-</span>
                </td>
                <td>                        
                    <genders 
                    m="team.gender !== 'female'"
                    f="team.gender !== 'male'"
                    ></genders>
                </td>
                <td ng-click="$event.stopPropagation()">                        
                    <i 
                        class="fa fa-print" 
                        ng-click="showCheckInList(team);"
                        ng-if="team.roster_team_id && !team.deleted && (team.status_entry !== 11)"
                        ng-show="!utils.validationProccess[team.roster_team_id]">
                    </i>
                    <spinner pos="left" size="1" active="utils.validationProccess[team.roster_team_id]"></spinner>
                </td>
                <td class="font-bold" ng-bind="::team.team_name"></td>
                <td ng-bind="::team.organization_code"></td>
                <td ng-bind="getTeamAgeLabel(team.age)"></td>
                <td ng-bind="::team.rank"></td>
                <td ng-click="$event.stopPropagation(); paymentMenu()" ng-if="!$parent.eventFinished()">
                    <status-paid status="{{team.status_paid}}"></status-paid>
                </td>
                <td ng-if="!$parent.eventFinished()">
                    <status-entry status="{{team.status_entry}}"></status-entry>
                </td>
                <td ng-if="!$parent.eventFinished()">
                    <span ng-if="team.is_local">Is local</span>
                    <span ng-if="!team.is_local">
                        <status-housing status="{{team.status_housing}}"></status-housing>
                    </span>
                </td>
            </tr>
            <tr ng-if="!teams.length">
                <td colspan="{{($parent.eventFinished()?8:10)}}" class="text-center">
                    <span class="text-info" ng-if="loaded"><i class="fa fa-info-circle"></i> No teams available for entrance</span>
                    <span ng-if="!loaded"><i class="fa fa-spinner fa-pulse fa-2x"></i></span>
                </td>
            </tr>
        </tbody>
    </table>
</fieldset>
