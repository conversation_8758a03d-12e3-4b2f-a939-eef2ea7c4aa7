class CustomFormBuilderService {
    constructor ($http, $uibModal, toastr) {
        this.$http = $http;
        this.$uibModal = $uibModal;
        this.$toastr = toastr;
    }
    getList (event, params) {
        return this.$http.get(`/api/custom-form-builder/event/${event}/form`, { params: params })
            .then(response => response.data);
    }

    getForm (event, formID) {
        return this.$http.get(`/api/custom-form-builder/event/${event}/form/${formID}`)
            .then(response => response.data && response.data.form);
    }

    updateForm (event, formID, data) {
        return this.$http.put(`/api/custom-form-builder/event/${event}/form/${formID}`, data);
    }

    createForm (event, data) {
        return this.$http.post(`/api/custom-form-builder/event/${event}/form`, data)
            .then(response => response.data && response.data.form);
    }

    deleteForm (event, formID) {
        return this.$http.delete(`/api/custom-form-builder/event/${event}/form/${formID}`);
    }

    createField (event, formID, data) {
        return this.$http.post(`/api/custom-form-builder/event/${event}/form/${formID}/field`, data)
            .then(response => response.data && response.data.field);
    }

    updateField (event, formID, fieldID, data) {
        return this.$http.put(`/api/custom-form-builder/event/${event}/form/${formID}/field/${fieldID}`, data);
    }

    deleteField (event, formID, fieldID) {
        return this.$http.delete(`/api/custom-form-builder/event/${event}/form/${formID}/field/${fieldID}`);
    }

    changeFieldSortOrder (event, formID, data) {
        return this.$http.put(`/api/custom-form-builder/event/${event}/form/${formID}/fields-order`, { fields: data });
    }

    openOptionsEditModal (fieldName = 'New Select Options', options, readOnly) {
        let self = this;

        return this.$uibModal.open({
            size: 'sm',
            template: `<modal-wrapper>
                            <custom-form-select-options 
                                options="options" 
                                on-save="onSave(options)" 
                                read-only="readOnly"
                                close="closeModal()"
                            ></custom-form-select-options>
                        </modal-wrapper>`,
            controller: ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
                scope.closeModal = function () {
                    $uibModalInstance.dismiss();
                }

                scope.modalTitle = `<h4>${fieldName}</h4>`;
                scope.options = angular.copy(options);
                scope.readOnly = readOnly;

                scope.onSave = (options) => {
                    self.$toastr.success('Success');

                    $uibModalInstance.close(options);
                }
            }]
        }).result.then(options => {
            return options;
        }, () => {})
    }

    async openFormEditingModal (event, formID) {
        const formData = await this.getForm(event, formID);
        let self = this;

        return this.$uibModal.open({
            backdrop: 'static',
            size: 'lg',
            template: `<div class="modal-body">
                            <custom-forms-editor 
                                form-data="formData" 
                                mode="mode" 
                                form-id="formID"
                                on-close="$close()"/>
                       </div>
                      `,
            controller: ['$scope', function (scope) {
                scope.formData = formData;
                scope.mode = 'update';
                scope.formID = formID;
            }]
        }).result.then(() => {}, () => {});
    }

    openFormCreationModal (event) {
        let self = this;

        return this.$uibModal.open({
            backdrop: 'static',
            size: 'lg',
            template: `<div class="modal-body">
                            <custom-forms-editor 
                                mode="mode" 
                                on-close="$close()"/>
                       </div>
                      `,
            controller: ['$scope', function (scope) {
                scope.mode = 'create';
            }]
        }).result.then(() => {}, () => {});
    }
}

CustomFormBuilderService.$inject = ['$http', '$uibModal', 'toastr'];

angular.module('SportWrench').service('customFormBuilderService', CustomFormBuilderService);
