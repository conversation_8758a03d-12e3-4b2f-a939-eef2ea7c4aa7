angular.module('SportWrench').service('EventTransfersService',
    ['$http', '$uibModal', '_', '$q', 'UtilsService', EventTransfersService]
);

function EventTransfersService ($http, $uibModal, _, $q, UtilsService) {
	this._$http  	= $http;
	this._$uibModal = $uibModal;
	this._  		= _;
	this._$q        = $q;
    this.UtilsService = UtilsService;
	this.baseUrl  	= 'api/event/';
}

EventTransfersService.prototype.getAccounts = function (eventID) {
	return this._$http.get(this.baseUrl + eventID + '/transfer/accounts');
}

EventTransfersService.prototype.makeTransfer = function (eventID, data) {
	return this._$http.post(this.baseUrl + eventID + '/transfer/create', data);
};

EventTransfersService.prototype.getTransfersList = function (eventID, type) {
	return this._$http.get(this.baseUrl + eventID + '/transfer/' + type + '/list');
};

EventTransfersService.prototype.openTransferModal = function (eventID, accountID, availableAmount, paymentFor) {
	var _self = this;

	return this._$uibModal.open({
		template: [
			'<modal-wrapper>',
			'<event-transfer-form funds-list="fundsList" on-submit="makeTransfer(data)"></event-transfer-form>',
			'</modal-wrapper>'
		].join(''),
		controller: ['$scope', function ($scope) {
			var makeTransfer = _self.makeTransfer.bind(_self, eventID);

			$scope.modalTitle 	= '<h4>Make a payout</h4>'
			$scope.fundsList 	= [{ amount: availableAmount, currency: 'usd' }];

			$scope.makeTransfer = function (data) {
				return makeTransfer(
					_self._.extend({ account_id: accountID, payment_for: paymentFor }, data)
				)
				.then(function () {
					$scope.$close(true);
				})
			}
		}]
	}).result;
};

EventTransfersService.prototype.getStatistics = function(eventID, type) {
	return this._$http.get(this.baseUrl + eventID + '/transfer/' + type + '/stats');
};

EventTransfersService.prototype.getStripeAccountData = function(eventID, type) {
    return this._$http.get(`${this.baseUrl + eventID}/transfer/account/${type}`);
};

EventTransfersService.prototype.loadTransfersData = function (eventID, type) {
    let usdAvailable;

    return this._$q.all([
        this.getStripeAccountData(eventID, type),
        this.getTransfersList(eventID, type),
        this.getStatistics(eventID, type)
    ]).then((result) => {
        let accData = result[0].data;

        let account = accData.account;
        let balance = accData.balance;
        let events = accData.events;

        if (balance) {
            let __usdAvailable = __getUSDAvailable__(balance.available);
            usdAvailable  = (__usdAvailable === null) ? 0 : __usdAvailable.amount;
            balance = this._formatBalanceObject(balance);
        }

        let transfers  = result[1].data.transfers;

        let stats = result[2].data.statistics[type];

        return {
            account,
            balance,
            events,
            usdAvailable: usdAvailable,
            stats       : stats,
            transfers   : transfers
        };
    })
};

EventTransfersService.prototype._formatBalanceObject = function (balance) {
    if(balance) {
        balance.totalAvailable = balance.available.reduce((sum, val) => {
            return this.UtilsService.approxNumber(sum + Number(val));
        }, 0);

        let __usdAvailable = this._getUSDAvailable(balance.available);
        balance.usdAvailable = (__usdAvailable === null) ? 0 : __usdAvailable.amount;
    }
    return balance;
};

EventTransfersService.prototype._getUSDAvailable = function(fundsList) {
    for (var i = 0; i < fundsList.length; ++i) {
        if (fundsList[i].currency === 'usd') {
            return fundsList[i];
        }
    }

    return null;
};

let __getUSDAvailable__ = function (fundsList) {
    for (var i = 0; i < fundsList.length; ++i) {
        if (fundsList[i].currency === 'usd') {
            return fundsList[i];
        }
    }

    return null;
};
