angular.module('SportWrench').directive('eventUsers', [
    'eventUsersService', '$stateParams', '$state', 'eventStrorage','APP_ROUTES','$rootScope',
    function (eventUsersService, $stateParams, $state, eventStrorage, APP_ROUTES, $rootScope) {
        return {
            restrict: 'E',
            templateUrl: 'events/settings/general/event-users/event-users.html',
            replace: true,
            link: function (scope) {
                scope.eventUsers    = {};
                scope.loading       = {
                    data_loaded: false,
                    error: false,
                    errMsg: ''
                };

                let eventID = $stateParams.event;

                eventUsersService.getEventUsers(eventID)
                    .then(function (data) {
                        let { users, eo, permissions } = data;

                        scope.eventUsers            = users;
                        scope.eoData                = eo;
                        scope.permissions           = permissions;
                        scope.loading.data_loaded   = true;

                        formatPermissionTitles();
                    })
                    .catch(function (err) {
                        scope.loading.data_loaded   = true;
                        scope.loading.error         = true;
                        scope.loading.errMsg        = (err.data && err.data.validation)
                                                        ? err.data.validation
                                                        : 'Internal Server Error.'
                    });

                scope.goToAddEventUser = function () {
                    $state.go(APP_ROUTES.EO.ADD_EVENT_USER, {
                        event: eventID
                    });
                };

                let formatPermissionTitles = function () {
                    scope.eventUsers.forEach(user => {
                        user.permissionsList = getPermissionTitles(user.permissions)
                    })
                };

                scope.openPermissionsEditModal = function (user) {
                    let userForUpdate = angular.copy(user);

                    eventUsersService.openPermissionsEdit(eventID, userForUpdate)
                        .then(updatePermissions.bind(null));
                };

                scope.getUserName = function (userData) {
                    return `${userData.first} ${userData.last}`;
                };

                scope.markAsDeleted = function (isConfirmed, user) {
                    if(!isConfirmed) {
                        return;
                    }

                    eventUsersService.markAsDeleted(eventID, user.email)
                        .then(() => removeUserFromList(user.email))
                        .catch(function (err) {
                            scope.loading.errMsg = (err.data && err.data.validation)
                                    ? err.data.validation
                                    : 'Internal Server Error.'
                        });
                };

                $rootScope.$on('eventUserAdded', function (event, user) {
                    user.permissionsList   = getPermissionTitles(user.permissions);
                    scope.eventUsers.push(user);
                });

                function getPermissionTitles (userPermissions) {
                    userPermissions = Object.keys(userPermissions);

                    if(userPermissions.length === scope.permissions.length) {
                        return ['All (same as EO)'];
                    }
                    return scope.permissions.reduce((acc, permission) => {
                        if(userPermissions.includes(permission.event_operation)) {
                            acc.push(permission.title);
                        }
                        return acc;
                    }, []);
                }

                function updatePermissions (params = {}) {
                    let { email, permissions: updatedUserPermissions } = params;

                    if(!email || !updatedUserPermissions) {
                        return;
                    }

                    if(_.isEmpty(updatedUserPermissions)) {
                        return removeUserFromList(email);
                    }

                    scope.eventUsers.forEach(eventUser => {
                        if(eventUser.email === email) {
                            eventUser.permissions       = updatedUserPermissions;
                            eventUser.permissionsList   = getPermissionTitles(eventUser.permissions)
                        }
                    })
                }

                function removeUserFromList (email) {
                    scope.eventUsers = _.filter(scope.eventUsers, userItem => userItem.email !== email);
                }
            }
        }
    }]);

