angular.module('SportWrench').service('v2OfficialService', OfficialService);

function OfficialService ($http, $uibModal, EventOfficialService, _, moment, ConfirmationService,
                          AVAILABLE_OFFICIALS_SANCTIONINGS, toastr) {
	this.$http 					= $http;
	this.$uibModal 				= $uibModal;
	this.EventOfficialService 	= EventOfficialService;
	this.ConfirmationService    = ConfirmationService;
	this._ 						= _;
	this.moment 				= moment;
	this.baseUrl 				=  '/api/official/event/';
    this.toastr                 = toastr;
    this.AVAILABLE_OFFICIALS_SANCTIONINGS = AVAILABLE_OFFICIALS_SANCTIONINGS;
}

OfficialService.prototype.signUpToEvent = function (eventID, data) {
	return this.$http.post(this.baseUrl + eventID + '/check_in', data);
};

OfficialService.prototype.exitEvent = function (eventID, data) {
	return this.$http.delete(this.baseUrl + eventID + '/check_out', { data });
};

OfficialService.prototype.updateRegInfo = function (eventID, data) {
	return this.$http.put(this.baseUrl + eventID + '/update', data);
};

OfficialService.prototype.formHotelNightsList = function (officialDates = {}, staffDates = {}) {
    let dates = {};

    if(_.isEmpty(officialDates) && !_.isEmpty(staffDates)) {
        dates = staffDates;

    } else if(!_.isEmpty(officialDates) && _.isEmpty(staffDates)) {
        dates = officialDates;

    } else if(!_.isEmpty(officialDates) && !_.isEmpty(staffDates)) {
        /**
         * If both roles are selected, the system should show union of the nights available for official and for staff,
         * e.g. if we have these days for Staff - 1st till 3rd and for Official 2nd till 4th - the list should be 1st till 4th.
         */
        dates = this.__mergeNightsDates(officialDates, staffDates);
    }

    return this._.map(dates, function (value, day) {
        return {
            date: day,
            paid: value,
            text: 'Night of ' + moment(day, 'MMDDYYYY').format('ddd, MMM DD', 'en')
        };
    })
};

OfficialService.prototype.showEventRegModal = function (regData) {
    const self = this;
    const allowOfficialRegistration = this.__allowOfficialRegistration(regData);
    const allowStaffRegistration = this.__allowStaffRegistration(regData);

    let disableOfficialRole = false;

    if(allowOfficialRegistration) {
        try {
            this.__validateSanctioningBody(regData);
        } catch (error) {
            if(!allowStaffRegistration) {
                this.toastr.warning(error.validation);

                return Promise.reject(error);
            }

            disableOfficialRole = true;
        }
    }

    return this.$uibModal.open({
        templateUrl: 'official/checkin/checkin-modal.html',
        controller: ['$scope', 'SAVED_MSG', 'toastr', '$rootScope', '$stateParams',
            function ($scope, SAVED_MSG, toastr, $rootScope, $stateParams) {
                var title 			= 'Registration Info',
                    checkinDataCache;

                let staffNightsObj      = {};
                let officialNightsObj   = {};
                let bothNightsObj       = {};

                $scope.loaded = true;

                $scope.modalTitle 	= '<h4>' + title + '</h4>';

                $scope.event 		            = regData.event || { days_dates: [] };
                $scope.checkinData 	            = { schedule_availability: {} };
                $scope.heads 		            = regData.heads || [];
                $scope.officialAdditionalRoles 	= [];
                $scope.disableOfficialRole      = disableOfficialRole;
                $scope.clothes                  = regData.clothes;
                $scope.officialData             = regData.official_data;
                $scope.modalTitle 	            = '<h4>' + title + ': ' + $scope.event.name + '</h4>';
                $scope.isUpdateMode             = false;
                $scope.profileData              = regData.profile_data;
                let roleData                    = regData.role_data;

                $scope.changeNights = function (is_staff, is_official) {
                    let nights = {};

                    if(is_staff && !is_official) {
                        nights = staffNightsObj;

                    } else if(!is_staff && is_official) {
                        nights = officialNightsObj;

                    } else if(is_staff && is_official) {
                        nights = bothNightsObj;
                    }

                    $scope.hotelNights = nights;
                };

                formatHotelNightsObjects(
                    $scope.event.staff_hotel_comp && $scope.event.staff_hotel_comp.dates,
                    $scope.event.officials_hotel_comp && $scope.event.officials_hotel_comp.dates,
                    $scope.event.enable_hotel_for_officials,
                    $scope.event.enable_hotel_for_staff
                );
                $scope.userGender = regData.user_gender;
                $scope.checkinData.arbiterpay_username = $scope.checkinData.arbiterpay_username ||
                    $scope.profileData.arbiter_pay_username;
                $scope.checkinData.arbiterpay_account_number = $scope.checkinData.arbiterpay_account_number ||
                    $scope.profileData.arbiter_pay_account_number;

                if (!self._.isEmpty(roleData)) {

                    $scope.isUpdateMode = isUpdateMode(roleData, $scope.event); // Visual
                    $scope.isOnUpdate   = true; // Logic

                    $scope.changeNights(roleData.is_staff, roleData.is_official);

                    if(roleData.staff_arrival_datetime) {
                        roleData.staff_arrival_datetime = convertToDate(roleData.staff_arrival_datetime, 'YYYY-MM-DD HH:mm:ss');
                    }

                    if (roleData.departure_datetime) {
                        roleData.departure_datetime = convertToDate(roleData.departure_datetime, 'YYYY-MM-DD HH:mm:ss');
                    }

                    if(roleData.deleted_staff_date || roleData.deleted_official_date) {
                        $scope.deletedMsg 	= getWithdrawMsg(
                            roleData.deleted_staff_date, roleData.deleted_official_date, $scope.event,
                            $scope.profileData
                        );

                        if($scope.deletedMsg) {
                            checkinDataCache = self._.omit(roleData, 'deleted');

                            if($scope.event.allow_staff_registration && $scope.profileData.user_has_staff_role) {
                                checkinDataCache.is_staff = !!roleData.deleted_staff_date || roleData.is_staff;
                            }

                            if($scope.event.allow_official_registration && $scope.profileData.user_has_official_role) {
                                checkinDataCache.is_official = !!roleData.deleted_official_date || roleData.is_official;
                            }

                        }
                    }

                    $scope.checkinData 	= self._.omit(roleData, 'deleted');
                }

                self.EventOfficialService.loadOfficialAdditionalRoles()
                    .then(function (resp) {
                        $scope.officialAdditionalRoles  = resp.data;
                    });

                function getWithdrawMsg (staffDate, officialDate, eventData, profileData) {
                    let dateFormat = 'Mon DD, YYYY';

                    let allowStaffRestore      = eventData.allow_staff_registration && profileData.user_has_staff_role;
                    let allowOfficialRestore   = eventData.allow_official_registration
                        && profileData.user_has_official_role;

                    if(staffDate && !officialDate && allowStaffRestore) {
                        return `You canceled your participation as Staff at ${eventData.name} on ${staffDate}.`;
                    }

                    if(!staffDate && officialDate && allowOfficialRestore) {
                        return `You canceled your participation as Official at ${eventData.name} on ${officialDate}.`;
                    }

                    if(officialDate && staffDate && (allowOfficialRestore || allowStaffRestore)) {
                        let date = moment(staffDate, dateFormat).isAfter(officialDate, dateFormat)
                            ? staffDate
                            : officialDate;

                        return `You canceled your participation at ${eventData.name} on ${date}.`;
                    }
                }

                $scope.restoreData = function () {
                    $scope.checkinData 	= angular.copy(checkinDataCache);
                    $scope.changeNights(checkinDataCache.is_staff, checkinDataCache.is_official);

                    checkinDataCache 	= undefined;
                    $scope.deletedMsg 	= undefined;
                };

                $scope.enterEvent = function (data) {
                    data.departure_datetime = removeTimezone(data.departure_datetime);

                    return self.signUpToEvent($stateParams.event, _.omit(data, 'deleted_staff_date', 'deleted_official_date'))
                        .then(function () {
                            toastr.success(SAVED_MSG);
                            $scope.$close(true);
                        });
                };

                $scope.exitEvent = function (role) {
                    const partialData = angular.extend({}, self.ConfirmationService.PARTIAL_DATA, { role });

                    return self.exitEvent($stateParams.event, partialData)
                        .then(function () {
                            toastr.success(SAVED_MSG);
                            $scope.$close(true);
                        });
                };

                $scope.updateReg = function (data) {
                    data.departure_datetime = removeTimezone(data.departure_datetime);

                    return self.updateRegInfo($stateParams.event, _.omit(data, 'deleted_staff_date', 'deleted_official_date'))
                        .then(function () {
                            toastr.success(SAVED_MSG);
                            $scope.$close(true);
                        });
                };

                $scope.initSubmit = function () {
                    $rootScope.$broadcast('form.submitted');
                };

                function formatHotelNightsObjects (staffDates, officialDates, enableOfficial, enableStaff) {
                    staffNightsObj      = enableStaff       ? self.formHotelNightsList(staffDates)      : {};
                    officialNightsObj   = enableOfficial    ? self.formHotelNightsList(officialDates)   : {};

                    if(enableStaff && enableOfficial) {
                        bothNightsObj = self.formHotelNightsList(staffDates, officialDates)

                    } else if(enableStaff && !enableOfficial) {
                        bothNightsObj = staffNightsObj;

                    } else if(!enableStaff && enableOfficial) {
                        bothNightsObj = officialNightsObj;

                    } else {
                        bothNightsObj = {};
                    }
                }

                function isUpdateMode (roleData) {
                    return roleData.is_official || roleData.is_staff;
                }

                $scope.getSubmitText = function () {
                    return $scope.isUpdateMode?'Update':'Apply';
                };

                $scope.getSubmitClass = function () {
                    return {
                        'btn pull-right': true,
                        'btn-success' 	: !$scope.isUpdateMode,
                        'btn-primary' 	: $scope.isUpdateMode
                    };
                };
            }]
    }).result;
};

OfficialService.prototype.__mergeNightsDates = function (official, staff) {
    let officialDates = Object.assign({}, official);
    let staffDates    = Object.assign({}, staff);
    let allDates      = Object.assign({}, officialDates, staffDates);

    let result = {};

    Object.keys(allDates).forEach(date => {
        if(typeof staffDates[date] === 'undefined') {
            staffDates[date] = false;
        }

        if(typeof officialDates[date] === 'undefined') {
            officialDates[date] = false;
        }

        result[date] = officialDates[date] || staffDates[date];
    });

    return result;
};

OfficialService.prototype.__allowOfficialRegistration = function (regData) {
    const {event, profile_data} = regData;

    return event.allow_official_registration && profile_data.user_has_official_role;
}

OfficialService.prototype.__allowStaffRegistration = function (regData) {
    const {event, profile_data} = regData;

    return event.allow_staff_registration && profile_data.user_has_staff_role;
}

OfficialService.prototype.__validateSanctioningBody = function (regData) {
    const {event: {available_officials_sanctionings}, profile_data: {usav_num, aau_number}} = regData

    if (_.isEmpty(available_officials_sanctionings)) {
        throw {validation: 'Event not have available officials sanctionings.'}
    }

    const eventOfficialsSanctioningsValidationRules = [
        available_officials_sanctionings.includes(this.AVAILABLE_OFFICIALS_SANCTIONINGS.USAV) && !!usav_num,
        available_officials_sanctionings.includes(this.AVAILABLE_OFFICIALS_SANCTIONINGS.AAU) && !!aau_number,
        available_officials_sanctionings.includes(this.AVAILABLE_OFFICIALS_SANCTIONINGS.UNSANCTIONED) && !usav_num && !aau_number,
    ];

    const isValid = eventOfficialsSanctioningsValidationRules.some(rule => rule);

    if(!isValid) {
        throw {validation: "You can't register for this event. Your account doesn't match the government body."}
    }
}

function convertToUTC (date, mask) {
    return moment.utc(date, mask).toDate();
}
function convertToDate (date, mask) {
    return moment(date, mask).toDate();
}

function removeTimezone (date) {
    return moment(date).format('YYYY-MM-DDTHH:mm:ss');
}
