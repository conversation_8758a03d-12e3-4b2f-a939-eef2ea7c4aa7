<table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
    <tr>
        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">
            <!-- CAPTION -->
            <table border="0" cellpadding="0" cellspacing="0" class="caption" style="font-family: sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 15px; text-align: center;border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
              <tr>
                <td><%= official.name %> has changed registration info for <%= eventName %>:</td>
              </tr>
            </table>
            <!-- CAPTION END -->

            <!-- COMPARATIVE TABLE -->
            <table border="0" cellpadding="0" cellspacing="0" width="600" class="responsiveTable">
                <tr class="table-caption">
                    <td align="center" width="50%"style="text-align: center; vertical-align: middle">
                        <table border="0" cellpadding="5" cellspacing="0" width="100%">
                            <tr>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                    <td align="center" valign="middle" width="50%" style="text-align: center; color: #999999; font-weight: bold">
                        <table border="0" cellpadding="5" cellspacing="0" width="100%">
                            <tr>
                                <td width="50%" colspan="2" valign="middle">Old Value:</td>
                                <td width="50%" colspan="2" valign="middle">New Value:</td>
                            </tr>
                        </table>
                    </td>
                </tr>
              <% for (var i = 0; i < difference.length; ++i) { %>
                <tr>
                    <td align="center" width="50%" class="column" style="text-align: center; vertical-align: middle">
                        <table border="0" cellpadding="5" cellspacing="0" width="100%">
                            <tr>
                                <td>
                                    <h3><%= difference[i].fieldName %></h3>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td align="center" valign="middle" width="50%" class="column" style="text-align: center">
                        <table border="0" cellpadding="5" cellspacing="0" width="100%">
                            <tr class="column-descr" style="height: 0;width:0; overflow:hidden;float:left; display:none">
                              <td width="50%" valign="middle">Old Value:</td>
                              <td width="50%" valign="middle">New Value:</td>
                            </tr>
                            <% if (difference[i].is_schedule == true) { %>
                                <!-- FORMAT SCHEDULE AVAILABILITY -->
                                <tr>
                                    <td width="50%" valign="middle">
                                        <% for (var dayIndex = 0; dayIndex < eventDates.length; ++dayIndex) { %>
                                            <p>
                                                <span><%= eventDates[dayIndex] %>:</span>
                                                <span><%= formatValue(difference[i].before[eventDates[dayIndex]]) %></span>
                                            </p>
                                        <% } %>
                                    </td>
                                    <td width="50%" valign="middle">
                                        <% for (var dayIndex = 0; dayIndex < eventDates.length; ++dayIndex) { %>
                                            <p>
                                                <span><%= eventDates[dayIndex] %>:</span>
                                                <span><%= formatValue(difference[i].after[eventDates[dayIndex]]) %></span>
                                            </p>
                                        <% } %>
                                    </td>
                                </tr>
                            <% } else { %>
                                <tr>
                                    <td width="50%" valign="middle">
                                       <p><%= formatValue(difference[i].before) %></p>
                                    </td>
                                    <td width="50%" valign="middle">
                                       <p><%= formatValue(difference[i].after) %></p>
                                    </td>
                                </tr>
                            <% } %>
                        </table>
                    </td>
                </tr>
              <% } %>
            </table>
            <!-- COMPARATIVE TABLE END -->

            <!-- FFICIAL INFO -->
            <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;" width="100%">
                <tbody>
                    <tr>
                        <td align="left" width="100%" style="font-family: sans-serif; font-size: 16px; font-weight: bold; vertical-align: top; width: 100%; padding-top: 15px; padding-bottom: 5px; padding-left: 25px; color: #999999" valign="top">
                            General Info:
                        </td>
                    </tr>
                </tbody>
            </table>
            <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;" width="100%">
                <tbody>
                    <tr>
                        <td align="left" width="100%" style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px; width: 100%" valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                                <tbody>
                                    <tr>
                                        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; width: 100%" valign="top" width="100%"> 
                                            <div style="border: solid 1px #999999; border-radius: 5px; box-sizing: border-box; font-size: 14px; margin: 0; padding: 12px 25px; border-color: #999999;">
                                                <% if (official.work_status) { %>
                                                    <p>
                                                        <span style="font-weight: bold">Work Status:</span>
                                                        <span><%= official.work_status %></span>
                                                    </p>
                                                <% } %>
                                                <% if (official.rank) { %>
                                                    <p>
                                                        <span style="font-weight: bold">Rank:</span>
                                                        <span><%= official.rank %></span>
                                                    </p>
                                                <% } %>
                                                <% if (official.usav_num) { %>
                                                    <p>
                                                        <span style="font-weight: bold">Usav Number:</span>
                                                        <span><%= official.usav_num %></span>
                                                    </p>
                                                <% } %>
                                                <p>
                                                    <span style="font-weight: bold">Address:</span>
                                                    <span><%= official.address %></span>
                                                </p>
                                            </div> 
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
            <!-- OFFICIAL INFO END -->
            
            <table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;" width="100%">
                <tbody>
                    <tr>
                        <td align="left" style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;" valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                                <tbody>
                                    <tr>
                                        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; background-color: #3498db; border-radius: 5px; text-align: center;" valign="top" bgcolor="#3498db" align="center"> <a href="<%= detailsLink %>" target="_blank" style="display: inline-block; color: #ffffff; background-color: #3498db; border: solid 1px #3498db; border-radius: 5px; box-sizing: border-box; cursor: pointer; text-decoration: none; font-size: 14px; font-weight: bold; margin: 0; padding: 12px 25px; text-transform: capitalize; border-color: #3498db;">Open Details On SW</a> 
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>


        </td>
    </tr>
</table>
