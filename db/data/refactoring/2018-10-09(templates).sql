BEGIN;


-- ADD "staff.declined" email template ------------------------------------------------------------------------
INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id, title, bee_json, email_template_type, is_valid, email_template_group, published) VALUES ('<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <!--[if gte mso 9]><xml>
     <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
     </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge" /><!--<![endif]-->
    <title>BF-basic-newsletter</title>


    <style type="text/css" id="media-query">
      body {
  margin: 0;
  padding: 0; }

table, tr, td {
  vertical-align: top;
  border-collapse: collapse; }

.ie-browser table, .mso-container table {
  table-layout: fixed; }

* {
  line-height: inherit; }

a[x-apple-data-detectors=true] {
  color: inherit !important;
  text-decoration: none !important; }

[owa] .img-container div, [owa] .img-container button {
  display: block !important; }

[owa] .fullwidth button {
  width: 100% !important; }

[owa] .block-grid .col {
  display: table-cell;
  float: none !important;
  vertical-align: top; }

.ie-browser .num12, .ie-browser .block-grid, [owa] .num12, [owa] .block-grid {
  width: 640px !important; }

.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
  line-height: 100%; }

.ie-browser .mixed-two-up .num4, [owa] .mixed-two-up .num4 {
  width: 212px !important; }

.ie-browser .mixed-two-up .num8, [owa] .mixed-two-up .num8 {
  width: 424px !important; }

.ie-browser .block-grid.two-up .col, [owa] .block-grid.two-up .col {
  width: 320px !important; }

.ie-browser .block-grid.three-up .col, [owa] .block-grid.three-up .col {
  width: 213px !important; }

.ie-browser .block-grid.four-up .col, [owa] .block-grid.four-up .col {
  width: 160px !important; }

.ie-browser .block-grid.five-up .col, [owa] .block-grid.five-up .col {
  width: 128px !important; }

.ie-browser .block-grid.six-up .col, [owa] .block-grid.six-up .col {
  width: 106px !important; }

.ie-browser .block-grid.seven-up .col, [owa] .block-grid.seven-up .col {
  width: 91px !important; }

.ie-browser .block-grid.eight-up .col, [owa] .block-grid.eight-up .col {
  width: 80px !important; }

.ie-browser .block-grid.nine-up .col, [owa] .block-grid.nine-up .col {
  width: 71px !important; }

.ie-browser .block-grid.ten-up .col, [owa] .block-grid.ten-up .col {
  width: 64px !important; }

.ie-browser .block-grid.eleven-up .col, [owa] .block-grid.eleven-up .col {
  width: 58px !important; }

.ie-browser .block-grid.twelve-up .col, [owa] .block-grid.twelve-up .col {
  width: 53px !important; }

@media only screen and (min-width: 660px) {
  .block-grid {
    width: 640px !important; }
  .block-grid .col {
    vertical-align: top; }
    .block-grid .col.num12 {
      width: 640px !important; }
  .block-grid.mixed-two-up .col.num4 {
    width: 212px !important; }
  .block-grid.mixed-two-up .col.num8 {
    width: 424px !important; }
  .block-grid.two-up .col {
    width: 320px !important; }
  .block-grid.three-up .col {
    width: 213px !important; }
  .block-grid.four-up .col {
    width: 160px !important; }
  .block-grid.five-up .col {
    width: 128px !important; }
  .block-grid.six-up .col {
    width: 106px !important; }
  .block-grid.seven-up .col {
    width: 91px !important; }
  .block-grid.eight-up .col {
    width: 80px !important; }
  .block-grid.nine-up .col {
    width: 71px !important; }
  .block-grid.ten-up .col {
    width: 64px !important; }
  .block-grid.eleven-up .col {
    width: 58px !important; }
  .block-grid.twelve-up .col {
    width: 53px !important; } }

@media (max-width: 660px) {
  .block-grid, .col {
    min-width: 320px !important;
    max-width: 100% !important;
    display: block !important; }
  .block-grid {
    width: calc(100% - 40px) !important; }
  .col {
    width: 100% !important; }
    .col > div {
      margin: 0 auto; }
  img.fullwidth, img.fullwidthOnMobile {
    max-width: 100% !important; }
  .no-stack .col {
    min-width: 0 !important;
    display: table-cell !important; }
  .no-stack.two-up .col {
    width: 50% !important; }
  .no-stack.mixed-two-up .col.num4 {
    width: 33% !important; }
  .no-stack.mixed-two-up .col.num8 {
    width: 66% !important; }
  .no-stack.three-up .col.num4 {
    width: 33% !important; }
  .no-stack.four-up .col.num3 {
    width: 25% !important; }
  .mobile_hide {
    min-height: 0px;
    max-height: 0px;
    max-width: 0px;
    display: none;
    overflow: hidden;
    font-size: 0px; } }

    </style>
</head>
<body class="clean-body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F7F7">
  <style type="text/css" id="media-query-bodytag">
    @media (max-width: 520px) {
      .block-grid {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

      .col {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

        .col > div {
          margin: 0 auto;
        }

      img.fullwidth {
        max-width: 100%!important;
      }
			img.fullwidthOnMobile {
        max-width: 100%!important;
      }
      .no-stack .col {
				min-width: 0!important;
				display: table-cell!important;
			}
			.no-stack.two-up .col {
				width: 50%!important;
			}
			.no-stack.mixed-two-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.mixed-two-up .col.num8 {
				width: 66%!important;
			}
			.no-stack.three-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.four-up .col.num3 {
				width: 25%!important;
			}
      .mobile_hide {
        min-height: 0px!important;
        max-height: 0px!important;
        max-width: 0px!important;
        display: none!important;
        overflow: hidden!important;
        font-size: 0px!important;
      }
    }
  </style>
  <!--[if IE]><div class="ie-browser"><![endif]-->
  <!--[if mso]><div class="mso-container"><![endif]-->
  <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F7F7;width: 100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr style="vertical-align: top">
		<td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]-->

    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: center"><strong><span style="font-size: 18px; line-height: 21px;">Your Staff Application for the {event_name} has been Declined!</span></strong></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 10px;padding-left: 10px;padding-top: 10px;padding-bottom: 10px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:0px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px"><span style="font-size: 14px; line-height: 16px;">Thank you for your interest in being part of our staff at the {event_name}. Unfortunately, at this time we do not an opening. We will certainly keep&#160;</span><span style="font-size: 14px; line-height: 16px;">your information and contact you should a staff position become available.</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:0px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:0px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px">Thank you,<br />{event_name}&#160;Staff</p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 5px;padding-left: 5px;padding-top: 5px;padding-bottom: 5px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px dotted #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 12px; line-height: 14px;">Copyright © SportWrench Inc. All rights reserved</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <a href="https://sportwrench.com" target="_blank">
    <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;width: 100%;max-width: 84px" width="84" />
  </a>
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
   <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
		</td>
  </tr>
  </tbody>
  </table>
  <!--[if (mso)|(IE)]></div><![endif]-->


</body></html>', '{event_name} Staff Registration', 'BF-basic-newsletter Your Staff Application for the {event_name} has been Declined! Thank you for your interest in being part of our staff at the {event_name}. Unfortunately, at this time we do not an opening. We will certainly keep  your information and contact you should a staff position become available. Thank you, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ', null, 'Staff Declined (new)', '{"page": {"body": {"type": "mailup-bee-page-properties", "content": {"style": {"color": "#000000", "font-family": "Tahoma, Verdana, Segoe, sans-serif"}, "computedStyle": {"linkColor": "#0000FF", "messageWidth": "640px", "messageBackgroundColor": "#FFFFFF"}}, "webFonts": [], "container": {"style": {"background-color": "#F7F7F7"}}}, "rows": [{"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: center;\"><strong><span style=\"font-size: 18px; line-height: 21px;\">Your Staff Application for the {event_name} has been Declined!</span></strong></p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "divider": {"style": {"width": "100%", "border-top": "1px solid #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "0px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px;\"><span style=\"font-size: 14px; line-height: 16px;\">Thank you for your interest in being part of our staff at the {event_name}. Unfortunately, at this time we do not an opening. We will certainly keep&nbsp;</span><span style=\"font-size: 14px; line-height: 16px;\">your information and contact you should a staff position become available.</span></p>\n</div>", "style": {"color": "#555555", "font-family": "Tahoma, Verdana, Segoe, sans-serif", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "0px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 14px; line-height: 16px;\">Thank you,<br />{event_name}&nbsp;Staff</p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "5px", "padding-left": "5px", "padding-right": "5px", "padding-bottom": "5px"}, "divider": {"style": {"width": "100%", "border-top": "1px dotted #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "0px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "0px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Logo", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "href": "https://sportwrench.com"}, "style": {"width": "100%", "padding-top": "0px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"class": "center  autowidth ", "width": 84}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}], "title": "BF-basic-newsletter", "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "description": "BF-basic-newsletter"}}', 'staff.declined', true, 'staff', true);
---------------------------------------------------------------------------------------------------------------


-- ADD "staff.accepted" email template ------------------------------------------------------------------------
INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id, title, bee_json, email_template_type, is_valid, email_template_group, published) VALUES ('<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <!--[if gte mso 9]><xml>
     <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
     </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge" /><!--<![endif]-->
    <title>BF-basic-newsletter</title>


    <style type="text/css" id="media-query">
      body {
  margin: 0;
  padding: 0; }

table, tr, td {
  vertical-align: top;
  border-collapse: collapse; }

.ie-browser table, .mso-container table {
  table-layout: fixed; }

* {
  line-height: inherit; }

a[x-apple-data-detectors=true] {
  color: inherit !important;
  text-decoration: none !important; }

[owa] .img-container div, [owa] .img-container button {
  display: block !important; }

[owa] .fullwidth button {
  width: 100% !important; }

[owa] .block-grid .col {
  display: table-cell;
  float: none !important;
  vertical-align: top; }

.ie-browser .num12, .ie-browser .block-grid, [owa] .num12, [owa] .block-grid {
  width: 640px !important; }

.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
  line-height: 100%; }

.ie-browser .mixed-two-up .num4, [owa] .mixed-two-up .num4 {
  width: 212px !important; }

.ie-browser .mixed-two-up .num8, [owa] .mixed-two-up .num8 {
  width: 424px !important; }

.ie-browser .block-grid.two-up .col, [owa] .block-grid.two-up .col {
  width: 320px !important; }

.ie-browser .block-grid.three-up .col, [owa] .block-grid.three-up .col {
  width: 213px !important; }

.ie-browser .block-grid.four-up .col, [owa] .block-grid.four-up .col {
  width: 160px !important; }

.ie-browser .block-grid.five-up .col, [owa] .block-grid.five-up .col {
  width: 128px !important; }

.ie-browser .block-grid.six-up .col, [owa] .block-grid.six-up .col {
  width: 106px !important; }

.ie-browser .block-grid.seven-up .col, [owa] .block-grid.seven-up .col {
  width: 91px !important; }

.ie-browser .block-grid.eight-up .col, [owa] .block-grid.eight-up .col {
  width: 80px !important; }

.ie-browser .block-grid.nine-up .col, [owa] .block-grid.nine-up .col {
  width: 71px !important; }

.ie-browser .block-grid.ten-up .col, [owa] .block-grid.ten-up .col {
  width: 64px !important; }

.ie-browser .block-grid.eleven-up .col, [owa] .block-grid.eleven-up .col {
  width: 58px !important; }

.ie-browser .block-grid.twelve-up .col, [owa] .block-grid.twelve-up .col {
  width: 53px !important; }

@media only screen and (min-width: 660px) {
  .block-grid {
    width: 640px !important; }
  .block-grid .col {
    vertical-align: top; }
    .block-grid .col.num12 {
      width: 640px !important; }
  .block-grid.mixed-two-up .col.num4 {
    width: 212px !important; }
  .block-grid.mixed-two-up .col.num8 {
    width: 424px !important; }
  .block-grid.two-up .col {
    width: 320px !important; }
  .block-grid.three-up .col {
    width: 213px !important; }
  .block-grid.four-up .col {
    width: 160px !important; }
  .block-grid.five-up .col {
    width: 128px !important; }
  .block-grid.six-up .col {
    width: 106px !important; }
  .block-grid.seven-up .col {
    width: 91px !important; }
  .block-grid.eight-up .col {
    width: 80px !important; }
  .block-grid.nine-up .col {
    width: 71px !important; }
  .block-grid.ten-up .col {
    width: 64px !important; }
  .block-grid.eleven-up .col {
    width: 58px !important; }
  .block-grid.twelve-up .col {
    width: 53px !important; } }

@media (max-width: 660px) {
  .block-grid, .col {
    min-width: 320px !important;
    max-width: 100% !important;
    display: block !important; }
  .block-grid {
    width: calc(100% - 40px) !important; }
  .col {
    width: 100% !important; }
    .col > div {
      margin: 0 auto; }
  img.fullwidth, img.fullwidthOnMobile {
    max-width: 100% !important; }
  .no-stack .col {
    min-width: 0 !important;
    display: table-cell !important; }
  .no-stack.two-up .col {
    width: 50% !important; }
  .no-stack.mixed-two-up .col.num4 {
    width: 33% !important; }
  .no-stack.mixed-two-up .col.num8 {
    width: 66% !important; }
  .no-stack.three-up .col.num4 {
    width: 33% !important; }
  .no-stack.four-up .col.num3 {
    width: 25% !important; }
  .mobile_hide {
    min-height: 0px;
    max-height: 0px;
    max-width: 0px;
    display: none;
    overflow: hidden;
    font-size: 0px; } }

    </style>
</head>
<body class="clean-body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F7F7">
  <style type="text/css" id="media-query-bodytag">
    @media (max-width: 520px) {
      .block-grid {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

      .col {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

        .col > div {
          margin: 0 auto;
        }

      img.fullwidth {
        max-width: 100%!important;
      }
			img.fullwidthOnMobile {
        max-width: 100%!important;
      }
      .no-stack .col {
				min-width: 0!important;
				display: table-cell!important;
			}
			.no-stack.two-up .col {
				width: 50%!important;
			}
			.no-stack.mixed-two-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.mixed-two-up .col.num8 {
				width: 66%!important;
			}
			.no-stack.three-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.four-up .col.num3 {
				width: 25%!important;
			}
      .mobile_hide {
        min-height: 0px!important;
        max-height: 0px!important;
        max-width: 0px!important;
        display: none!important;
        overflow: hidden!important;
        font-size: 0px!important;
      }
    }
  </style>
  <!--[if IE]><div class="ie-browser"><![endif]-->
  <!--[if mso]><div class="mso-container"><![endif]-->
  <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F7F7;width: 100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr style="vertical-align: top">
		<td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]-->

    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: center"><span style="font-size: 18px; line-height: 21px;"><strong><span style="line-height: 21px; font-size: 18px;">Welcome to the {event_name} our team!</span></strong></span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 10px;padding-left: 10px;padding-top: 10px;padding-bottom: 10px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/ok_animated.gif" alt="Image" title="Image" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: 0;height: auto;float: none;width: 100%;max-width: 250px" width="250" />
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;text-align:justify;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;"><span style="font-size:14px; line-height:17px;">{event_name} is excited to confirm of your attendance at the event as a part of our staff.<br />We look forward to seeing you in {event_city} in {event_month}!</span></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px">Thanks,<br />{event_name}&#160;Staff</p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 5px;padding-left: 5px;padding-top: 5px;padding-bottom: 5px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px dotted #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 12px; line-height: 14px;">Copyright © SportWrench Inc. All rights reserved</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <a href="https://sportwrench.com" target="_blank">
    <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;width: 100%;max-width: 84px" width="84" />
  </a>
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
   <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
		</td>
  </tr>
  </tbody>
  </table>
  <!--[if (mso)|(IE)]></div><![endif]-->


</body></html>', '{event_name} Staff Registration', 'BF-basic-newsletter Welcome to the {event_name} our team! {event_name} is excited to confirm of your attendance at the event as a part of our staff. We look forward to seeing you in {event_city} in {event_month}! Thanks, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ', null, 'Staff Accepted (new)', '{"page": {"body": {"type": "mailup-bee-page-properties", "content": {"style": {"color": "#000000", "font-family": "Tahoma, Verdana, Segoe, sans-serif"}, "computedStyle": {"linkColor": "#0000FF", "messageWidth": "640px", "messageBackgroundColor": "#FFFFFF"}}, "webFonts": [], "container": {"style": {"background-color": "#F7F7F7"}}}, "rows": [{"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: center;\"><span style=\"font-size: 18px; line-height: 21px;\"><strong><span style=\"line-height: 21px; font-size: 18px;\">Welcome to the <code data-bee-code=\"\">{event_name} our team</code>!</span></strong></span></p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "divider": {"style": {"width": "100%", "border-top": "1px solid #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Image", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/ok_animated.gif", "href": ""}, "style": {"width": "100%"}, "computedStyle": {"class": "center  autowidth ", "width": 250}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><span style=\"font-size: 14px; line-height: 16px;\">{event_name} is excited to confirm of your attendance at the event as a part of our staff.<br />We look forward to seeing you in {event_city} in {event_month}!</span></div>", "style": {"color": "#555555", "font-family": "Tahoma, Verdana, Segoe, sans-serif", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Thanks,<br>{event_name}&nbsp;Staff</p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "5px", "padding-left": "5px", "padding-right": "5px", "padding-bottom": "5px"}, "divider": {"style": {"width": "100%", "border-top": "1px dotted #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "0px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "0px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Logo", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "href": "https://sportwrench.com"}, "style": {"width": "100%", "padding-top": "0px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"class": "center  autowidth ", "width": 84}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}], "title": "BF-basic-newsletter", "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "description": "BF-basic-newsletter"}}', 'staff.accepted', true, 'staff', true);
---------------------------------------------------------------------------------------------------------------


-- ADD "staff.applied" email template -------------------------------------------------------------------------
INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id, title, bee_json, email_template_type, is_valid, email_template_group, published) VALUES ('<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <!--[if gte mso 9]><xml>
     <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
     </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge" /><!--<![endif]-->
    <title>BF-basic-newsletter</title>


    <style type="text/css" id="media-query">
      body {
  margin: 0;
  padding: 0; }

table, tr, td {
  vertical-align: top;
  border-collapse: collapse; }

.ie-browser table, .mso-container table {
  table-layout: fixed; }

* {
  line-height: inherit; }

a[x-apple-data-detectors=true] {
  color: inherit !important;
  text-decoration: none !important; }

[owa] .img-container div, [owa] .img-container button {
  display: block !important; }

[owa] .fullwidth button {
  width: 100% !important; }

[owa] .block-grid .col {
  display: table-cell;
  float: none !important;
  vertical-align: top; }

.ie-browser .num12, .ie-browser .block-grid, [owa] .num12, [owa] .block-grid {
  width: 640px !important; }

.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
  line-height: 100%; }

.ie-browser .mixed-two-up .num4, [owa] .mixed-two-up .num4 {
  width: 212px !important; }

.ie-browser .mixed-two-up .num8, [owa] .mixed-two-up .num8 {
  width: 424px !important; }

.ie-browser .block-grid.two-up .col, [owa] .block-grid.two-up .col {
  width: 320px !important; }

.ie-browser .block-grid.three-up .col, [owa] .block-grid.three-up .col {
  width: 213px !important; }

.ie-browser .block-grid.four-up .col, [owa] .block-grid.four-up .col {
  width: 160px !important; }

.ie-browser .block-grid.five-up .col, [owa] .block-grid.five-up .col {
  width: 128px !important; }

.ie-browser .block-grid.six-up .col, [owa] .block-grid.six-up .col {
  width: 106px !important; }

.ie-browser .block-grid.seven-up .col, [owa] .block-grid.seven-up .col {
  width: 91px !important; }

.ie-browser .block-grid.eight-up .col, [owa] .block-grid.eight-up .col {
  width: 80px !important; }

.ie-browser .block-grid.nine-up .col, [owa] .block-grid.nine-up .col {
  width: 71px !important; }

.ie-browser .block-grid.ten-up .col, [owa] .block-grid.ten-up .col {
  width: 64px !important; }

.ie-browser .block-grid.eleven-up .col, [owa] .block-grid.eleven-up .col {
  width: 58px !important; }

.ie-browser .block-grid.twelve-up .col, [owa] .block-grid.twelve-up .col {
  width: 53px !important; }

@media only screen and (min-width: 660px) {
  .block-grid {
    width: 640px !important; }
  .block-grid .col {
    vertical-align: top; }
    .block-grid .col.num12 {
      width: 640px !important; }
  .block-grid.mixed-two-up .col.num4 {
    width: 212px !important; }
  .block-grid.mixed-two-up .col.num8 {
    width: 424px !important; }
  .block-grid.two-up .col {
    width: 320px !important; }
  .block-grid.three-up .col {
    width: 213px !important; }
  .block-grid.four-up .col {
    width: 160px !important; }
  .block-grid.five-up .col {
    width: 128px !important; }
  .block-grid.six-up .col {
    width: 106px !important; }
  .block-grid.seven-up .col {
    width: 91px !important; }
  .block-grid.eight-up .col {
    width: 80px !important; }
  .block-grid.nine-up .col {
    width: 71px !important; }
  .block-grid.ten-up .col {
    width: 64px !important; }
  .block-grid.eleven-up .col {
    width: 58px !important; }
  .block-grid.twelve-up .col {
    width: 53px !important; } }

@media (max-width: 660px) {
  .block-grid, .col {
    min-width: 320px !important;
    max-width: 100% !important;
    display: block !important; }
  .block-grid {
    width: calc(100% - 40px) !important; }
  .col {
    width: 100% !important; }
    .col > div {
      margin: 0 auto; }
  img.fullwidth, img.fullwidthOnMobile {
    max-width: 100% !important; }
  .no-stack .col {
    min-width: 0 !important;
    display: table-cell !important; }
  .no-stack.two-up .col {
    width: 50% !important; }
  .no-stack.mixed-two-up .col.num4 {
    width: 33% !important; }
  .no-stack.mixed-two-up .col.num8 {
    width: 66% !important; }
  .no-stack.three-up .col.num4 {
    width: 33% !important; }
  .no-stack.four-up .col.num3 {
    width: 25% !important; }
  .mobile_hide {
    min-height: 0px;
    max-height: 0px;
    max-width: 0px;
    display: none;
    overflow: hidden;
    font-size: 0px; } }

    </style>
</head>
<body class="clean-body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F7F7">
  <style type="text/css" id="media-query-bodytag">
    @media (max-width: 520px) {
      .block-grid {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

      .col {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

        .col > div {
          margin: 0 auto;
        }

      img.fullwidth {
        max-width: 100%!important;
      }
			img.fullwidthOnMobile {
        max-width: 100%!important;
      }
      .no-stack .col {
				min-width: 0!important;
				display: table-cell!important;
			}
			.no-stack.two-up .col {
				width: 50%!important;
			}
			.no-stack.mixed-two-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.mixed-two-up .col.num8 {
				width: 66%!important;
			}
			.no-stack.three-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.four-up .col.num3 {
				width: 25%!important;
			}
      .mobile_hide {
        min-height: 0px!important;
        max-height: 0px!important;
        max-width: 0px!important;
        display: none!important;
        overflow: hidden!important;
        font-size: 0px!important;
      }
    }
  </style>
  <!--[if IE]><div class="ie-browser"><![endif]-->
  <!--[if mso]><div class="mso-container"><![endif]-->
  <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F7F7;width: 100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr style="vertical-align: top">
		<td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]-->

    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 18px; line-height: 21px;"><strong><span style="line-height: 21px; font-size: 18px;">Thank you for applying as a Staff member at the {event_name}.</span></strong></span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 10px;padding-left: 10px;padding-top: 10px;padding-bottom: 10px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: justify"><span style="font-size: 14px; line-height: 16px;">We will review your application shorty and send an email confirming the status of your staff position..</span></p><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: justify"><span style="font-size: 14px; line-height: 16px;">Email us us directly at {event_email} if you have any questions.</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: left">Thanks,<br />{event_name}&#160;Staff</p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 5px;padding-left: 5px;padding-top: 5px;padding-bottom: 5px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px dotted #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span>&#160;</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top: 5px; padding-bottom: 0px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 0px; padding-left: 0px; padding-top: 5px; padding-bottom: 0px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 12px; line-height: 14px;"><span style="line-height: 14px; font-size: 12px;">© </span>SportWrench Inc. All rights reserved</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <a href="https://sportwrench.com" target="_blank">
    <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;width: 100%;max-width: 84px" width="84" />
  </a>
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
   <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
		</td>
  </tr>
  </tbody>
  </table>
  <!--[if (mso)|(IE)]></div><![endif]-->


</body></html>', '{event_name} Staff Registration', 'BF-basic-newsletter Thank you for applying as a Staff member at the {event_name}. We will review your application shorty and send an email confirming the status of your staff position.. Email us us directly at {event_email} if you have any questions. Thanks, {event_name} Staff   © SportWrench Inc. All rights reserved https://sportwrench.com ', null, 'Staff Applied (new)', '{"page": {"body": {"type": "mailup-bee-page-properties", "content": {"style": {"color": "#000000", "font-family": "Tahoma, Verdana, Segoe, sans-serif"}, "computedStyle": {"linkColor": "#0000FF", "messageWidth": "640px", "messageBackgroundColor": "#FFFFFF"}}, "webFonts": [], "container": {"style": {"background-color": "#F7F7F7"}}}, "rows": [{"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 18px; line-height: 21px;\"><strong><span style=\"line-height: 21px; font-size: 18px;\">Thank you for applying as a Staff member at the {event_name}.</span></strong></span></p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "divider": {"style": {"width": "100%", "border-top": "1px solid #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><span style=\"font-size: 14px; line-height: 16px;\">We will review your application shorty and send an email confirming the status of your staff position..</span></p>\n<p style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><span style=\"font-size: 14px; line-height: 16px;\">Email us us directly at {event_email} if you have any questions.</span></p>\n</div>", "style": {"color": "#555555", "font-family": "Tahoma, Verdana, Segoe, sans-serif", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}, {"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: left;\">Thanks,<br>{event_name}&nbsp;Staff</p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "5px", "padding-left": "5px", "padding-right": "5px", "padding-bottom": "5px"}, "divider": {"style": {"width": "100%", "height": "0px", "border-top": "1px dotted #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><span style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\">© ﻿</span>SportWrench Inc. All rights reserved</span></p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "5px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Logo", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "href": "https://sportwrench.com"}, "style": {"width": "100%", "padding-top": "0px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"class": "center  autowidth ", "width": 84}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}], "title": "BF-basic-newsletter", "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "description": "BF-basic-newsletter"}}', 'staff.applied', true, 'staff', true);
---------------------------------------------------------------------------------------------------------------


-- ADD "staff.waitlisted" email template ----------------------------------------------------------------------
INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id, title, bee_json, email_template_type, is_valid, email_template_group, published) VALUES ('<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <!--[if gte mso 9]><xml>
     <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
     </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge" /><!--<![endif]-->
    <title>BF-basic-newsletter</title>


    <style type="text/css" id="media-query">
      body {
  margin: 0;
  padding: 0; }

table, tr, td {
  vertical-align: top;
  border-collapse: collapse; }

.ie-browser table, .mso-container table {
  table-layout: fixed; }

* {
  line-height: inherit; }

a[x-apple-data-detectors=true] {
  color: inherit !important;
  text-decoration: none !important; }

[owa] .img-container div, [owa] .img-container button {
  display: block !important; }

[owa] .fullwidth button {
  width: 100% !important; }

[owa] .block-grid .col {
  display: table-cell;
  float: none !important;
  vertical-align: top; }

.ie-browser .num12, .ie-browser .block-grid, [owa] .num12, [owa] .block-grid {
  width: 640px !important; }

.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
  line-height: 100%; }

.ie-browser .mixed-two-up .num4, [owa] .mixed-two-up .num4 {
  width: 212px !important; }

.ie-browser .mixed-two-up .num8, [owa] .mixed-two-up .num8 {
  width: 424px !important; }

.ie-browser .block-grid.two-up .col, [owa] .block-grid.two-up .col {
  width: 320px !important; }

.ie-browser .block-grid.three-up .col, [owa] .block-grid.three-up .col {
  width: 213px !important; }

.ie-browser .block-grid.four-up .col, [owa] .block-grid.four-up .col {
  width: 160px !important; }

.ie-browser .block-grid.five-up .col, [owa] .block-grid.five-up .col {
  width: 128px !important; }

.ie-browser .block-grid.six-up .col, [owa] .block-grid.six-up .col {
  width: 106px !important; }

.ie-browser .block-grid.seven-up .col, [owa] .block-grid.seven-up .col {
  width: 91px !important; }

.ie-browser .block-grid.eight-up .col, [owa] .block-grid.eight-up .col {
  width: 80px !important; }

.ie-browser .block-grid.nine-up .col, [owa] .block-grid.nine-up .col {
  width: 71px !important; }

.ie-browser .block-grid.ten-up .col, [owa] .block-grid.ten-up .col {
  width: 64px !important; }

.ie-browser .block-grid.eleven-up .col, [owa] .block-grid.eleven-up .col {
  width: 58px !important; }

.ie-browser .block-grid.twelve-up .col, [owa] .block-grid.twelve-up .col {
  width: 53px !important; }

@media only screen and (min-width: 660px) {
  .block-grid {
    width: 640px !important; }
  .block-grid .col {
    vertical-align: top; }
    .block-grid .col.num12 {
      width: 640px !important; }
  .block-grid.mixed-two-up .col.num4 {
    width: 212px !important; }
  .block-grid.mixed-two-up .col.num8 {
    width: 424px !important; }
  .block-grid.two-up .col {
    width: 320px !important; }
  .block-grid.three-up .col {
    width: 213px !important; }
  .block-grid.four-up .col {
    width: 160px !important; }
  .block-grid.five-up .col {
    width: 128px !important; }
  .block-grid.six-up .col {
    width: 106px !important; }
  .block-grid.seven-up .col {
    width: 91px !important; }
  .block-grid.eight-up .col {
    width: 80px !important; }
  .block-grid.nine-up .col {
    width: 71px !important; }
  .block-grid.ten-up .col {
    width: 64px !important; }
  .block-grid.eleven-up .col {
    width: 58px !important; }
  .block-grid.twelve-up .col {
    width: 53px !important; } }

@media (max-width: 660px) {
  .block-grid, .col {
    min-width: 320px !important;
    max-width: 100% !important;
    display: block !important; }
  .block-grid {
    width: calc(100% - 40px) !important; }
  .col {
    width: 100% !important; }
    .col > div {
      margin: 0 auto; }
  img.fullwidth, img.fullwidthOnMobile {
    max-width: 100% !important; }
  .no-stack .col {
    min-width: 0 !important;
    display: table-cell !important; }
  .no-stack.two-up .col {
    width: 50% !important; }
  .no-stack.mixed-two-up .col.num4 {
    width: 33% !important; }
  .no-stack.mixed-two-up .col.num8 {
    width: 66% !important; }
  .no-stack.three-up .col.num4 {
    width: 33% !important; }
  .no-stack.four-up .col.num3 {
    width: 25% !important; }
  .mobile_hide {
    min-height: 0px;
    max-height: 0px;
    max-width: 0px;
    display: none;
    overflow: hidden;
    font-size: 0px; } }

    </style>
</head>
<body class="clean-body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F7F7">
  <style type="text/css" id="media-query-bodytag">
    @media (max-width: 520px) {
      .block-grid {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

      .col {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

        .col > div {
          margin: 0 auto;
        }

      img.fullwidth {
        max-width: 100%!important;
      }
			img.fullwidthOnMobile {
        max-width: 100%!important;
      }
      .no-stack .col {
				min-width: 0!important;
				display: table-cell!important;
			}
			.no-stack.two-up .col {
				width: 50%!important;
			}
			.no-stack.mixed-two-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.mixed-two-up .col.num8 {
				width: 66%!important;
			}
			.no-stack.three-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.four-up .col.num3 {
				width: 25%!important;
			}
      .mobile_hide {
        min-height: 0px!important;
        max-height: 0px!important;
        max-width: 0px!important;
        display: none!important;
        overflow: hidden!important;
        font-size: 0px!important;
      }
    }
  </style>
  <!--[if IE]><div class="ie-browser"><![endif]-->
  <!--[if mso]><div class="mso-container"><![endif]-->
  <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F7F7;width: 100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr style="vertical-align: top">
		<td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]-->

    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: center"><strong><span style="font-size: 18px; line-height: 21px;">Your Staff Application for the {event_name} has been Waitlisted!</span></strong></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 10px;padding-left: 10px;padding-top: 10px;padding-bottom: 10px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: justify"><span style="font-size: 14px; line-height: 16px;">We have received your staff application for the {event_name} and are in the process of evaluating our staffing needs for the event, and will make a final decision as soon as possible. At this time we have placed you on a Waitlist.</span></p><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: justify"><br /><span style="font-size: 14px; line-height: 16px;">If you no longer want to be considered for the event, please log back in to sportwrench.com and withdraw your application.</span></p><p style="margin: 0;font-size: 12px;line-height: 14px;text-align: justify"><br /><span style="font-size: 14px; line-height: 16px;">Thank you for your interest in the {event_name} and we will get back to you shortly.</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px">Thank you,<br />{event_name}&#160;Staff</p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 5px;padding-left: 5px;padding-top: 5px;padding-bottom: 5px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px dotted #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 12px; line-height: 14px;">Copyright © SportWrench Inc. All rights reserved</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <a href="https://sportwrench.com" target="_blank">
    <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;width: 100%;max-width: 84px" width="84" />
  </a>
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
   <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
		</td>
  </tr>
  </tbody>
  </table>
  <!--[if (mso)|(IE)]></div><![endif]-->


</body></html>', '{event_name} Staff Registration', 'BF-basic-newsletter Your Staff Application for the {event_name} has been Waitlisted! We have received your staff application for the {event_name} and are in the process of evaluating our staffing needs for the event, and will make a final decision as soon as possible. At this time we have placed you on a Waitlist. If you no longer want to be considered for the event, please log back in to sportwrench.com and withdraw your application. Thank you for your interest in the {event_name} and we will get back to you shortly. Thank you, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ', null, 'Staff Waitlisted (new)', '{"page": {"body": {"type": "mailup-bee-page-properties", "content": {"style": {"color": "#000000", "font-family": "Tahoma, Verdana, Segoe, sans-serif"}, "computedStyle": {"linkColor": "#0000FF", "messageWidth": "640px", "messageBackgroundColor": "#FFFFFF"}}, "webFonts": [], "container": {"style": {"background-color": "#F7F7F7"}}}, "rows": [{"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: center;\"><strong><span style=\"font-size: 18px; line-height: 21px;\">Your Staff Application for the {event_name} has been Waitlisted!</span></strong></p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "divider": {"style": {"width": "100%", "border-top": "1px solid #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><span style=\"font-size: 14px; line-height: 16px;\">We have received your staff application for the {event_name} and are in the process of evaluating our staffing needs for the event, and will make a final decision as soon as possible. At this time we have placed you on a Waitlist.</span></p>\n<p style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><br /><span style=\"font-size: 14px; line-height: 16px;\">If you no longer want to be considered for the event, please log back in to sportwrench.com and withdraw your application.</span></p>\n<p style=\"font-size: 12px; line-height: 14px; text-align: justify;\"><br /><span style=\"font-size: 14px; line-height: 16px;\">Thank you for your interest in the {event_name} and we will get back to you shortly.</span></p>\n</div>", "style": {"color": "#555555", "font-family": "Tahoma, Verdana, Segoe, sans-serif", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 14px; line-height: 16px;\">Thank you,<br />{event_name}&nbsp;Staff</p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "5px", "padding-left": "5px", "padding-right": "5px", "padding-bottom": "5px"}, "divider": {"style": {"width": "100%", "border-top": "1px dotted #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "0px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "0px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Logo", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "href": "https://sportwrench.com"}, "style": {"width": "100%", "padding-top": "0px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"class": "center  autowidth ", "width": 84}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}], "title": "BF-basic-newsletter", "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "description": "BF-basic-newsletter"}}', 'staff.waitlisted', true, 'staff', true);
---------------------------------------------------------------------------------------------------------------


-- ADD "staff.withdrew" email template ------------------------------------------------------------------------
INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id, title, bee_json, email_template_type, is_valid, email_template_group, published) VALUES ('<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <!--[if gte mso 9]><xml>
     <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
     </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge" /><!--<![endif]-->
    <title>BF-basic-newsletter</title>


    <style type="text/css" id="media-query">
      body {
  margin: 0;
  padding: 0; }

table, tr, td {
  vertical-align: top;
  border-collapse: collapse; }

.ie-browser table, .mso-container table {
  table-layout: fixed; }

* {
  line-height: inherit; }

a[x-apple-data-detectors=true] {
  color: inherit !important;
  text-decoration: none !important; }

[owa] .img-container div, [owa] .img-container button {
  display: block !important; }

[owa] .fullwidth button {
  width: 100% !important; }

[owa] .block-grid .col {
  display: table-cell;
  float: none !important;
  vertical-align: top; }

.ie-browser .num12, .ie-browser .block-grid, [owa] .num12, [owa] .block-grid {
  width: 640px !important; }

.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
  line-height: 100%; }

.ie-browser .mixed-two-up .num4, [owa] .mixed-two-up .num4 {
  width: 212px !important; }

.ie-browser .mixed-two-up .num8, [owa] .mixed-two-up .num8 {
  width: 424px !important; }

.ie-browser .block-grid.two-up .col, [owa] .block-grid.two-up .col {
  width: 320px !important; }

.ie-browser .block-grid.three-up .col, [owa] .block-grid.three-up .col {
  width: 213px !important; }

.ie-browser .block-grid.four-up .col, [owa] .block-grid.four-up .col {
  width: 160px !important; }

.ie-browser .block-grid.five-up .col, [owa] .block-grid.five-up .col {
  width: 128px !important; }

.ie-browser .block-grid.six-up .col, [owa] .block-grid.six-up .col {
  width: 106px !important; }

.ie-browser .block-grid.seven-up .col, [owa] .block-grid.seven-up .col {
  width: 91px !important; }

.ie-browser .block-grid.eight-up .col, [owa] .block-grid.eight-up .col {
  width: 80px !important; }

.ie-browser .block-grid.nine-up .col, [owa] .block-grid.nine-up .col {
  width: 71px !important; }

.ie-browser .block-grid.ten-up .col, [owa] .block-grid.ten-up .col {
  width: 64px !important; }

.ie-browser .block-grid.eleven-up .col, [owa] .block-grid.eleven-up .col {
  width: 58px !important; }

.ie-browser .block-grid.twelve-up .col, [owa] .block-grid.twelve-up .col {
  width: 53px !important; }

@media only screen and (min-width: 660px) {
  .block-grid {
    width: 640px !important; }
  .block-grid .col {
    vertical-align: top; }
    .block-grid .col.num12 {
      width: 640px !important; }
  .block-grid.mixed-two-up .col.num4 {
    width: 212px !important; }
  .block-grid.mixed-two-up .col.num8 {
    width: 424px !important; }
  .block-grid.two-up .col {
    width: 320px !important; }
  .block-grid.three-up .col {
    width: 213px !important; }
  .block-grid.four-up .col {
    width: 160px !important; }
  .block-grid.five-up .col {
    width: 128px !important; }
  .block-grid.six-up .col {
    width: 106px !important; }
  .block-grid.seven-up .col {
    width: 91px !important; }
  .block-grid.eight-up .col {
    width: 80px !important; }
  .block-grid.nine-up .col {
    width: 71px !important; }
  .block-grid.ten-up .col {
    width: 64px !important; }
  .block-grid.eleven-up .col {
    width: 58px !important; }
  .block-grid.twelve-up .col {
    width: 53px !important; } }

@media (max-width: 660px) {
  .block-grid, .col {
    min-width: 320px !important;
    max-width: 100% !important;
    display: block !important; }
  .block-grid {
    width: calc(100% - 40px) !important; }
  .col {
    width: 100% !important; }
    .col > div {
      margin: 0 auto; }
  img.fullwidth, img.fullwidthOnMobile {
    max-width: 100% !important; }
  .no-stack .col {
    min-width: 0 !important;
    display: table-cell !important; }
  .no-stack.two-up .col {
    width: 50% !important; }
  .no-stack.mixed-two-up .col.num4 {
    width: 33% !important; }
  .no-stack.mixed-two-up .col.num8 {
    width: 66% !important; }
  .no-stack.three-up .col.num4 {
    width: 33% !important; }
  .no-stack.four-up .col.num3 {
    width: 25% !important; }
  .mobile_hide {
    min-height: 0px;
    max-height: 0px;
    max-width: 0px;
    display: none;
    overflow: hidden;
    font-size: 0px; } }

    </style>
</head>
<body class="clean-body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F7F7">
  <style type="text/css" id="media-query-bodytag">
    @media (max-width: 520px) {
      .block-grid {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

      .col {
        min-width: 320px!important;
        max-width: 100%!important;
        width: 100%!important;
        display: block!important;
      }

        .col > div {
          margin: 0 auto;
        }

      img.fullwidth {
        max-width: 100%!important;
      }
			img.fullwidthOnMobile {
        max-width: 100%!important;
      }
      .no-stack .col {
				min-width: 0!important;
				display: table-cell!important;
			}
			.no-stack.two-up .col {
				width: 50%!important;
			}
			.no-stack.mixed-two-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.mixed-two-up .col.num8 {
				width: 66%!important;
			}
			.no-stack.three-up .col.num4 {
				width: 33%!important;
			}
			.no-stack.four-up .col.num3 {
				width: 25%!important;
			}
      .mobile_hide {
        min-height: 0px!important;
        max-height: 0px!important;
        max-width: 0px!important;
        display: none!important;
        overflow: hidden!important;
        font-size: 0px!important;
      }
    }
  </style>
  <!--[if IE]><div class="ie-browser"><![endif]-->
  <!--[if mso]><div class="mso-container"><![endif]-->
  <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F7F7;width: 100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr style="vertical-align: top">
		<td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]-->

    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 18px; line-height: 21px;"><strong><span style="line-height: 21px; font-size: 18px;">You Have Withdrawn from {event_name}</span></strong></span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 10px;padding-left: 10px;padding-top: 10px;padding-bottom: 10px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 12px;line-height: 14px"><span style="font-size: 14px; line-height: 16px;">We have received your withdrawal notice from {event_name}. Thank you for your initial interest and hopefully we can welcome you as part of our&#160;</span><span style="font-size: 14px; line-height: 16px;">staff at another time.</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;"><![endif]-->
	<div style="color:#555555;line-height:120%;font-family:Tahoma, Verdana, Segoe, sans-serif; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px">Thank you,<br />{event_name} Staff</p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->



<table border="0" cellpadding="0" cellspacing="0" width="100%" class="divider " style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
    <tbody>
        <tr style="vertical-align: top">
            <td class="divider_inner" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;padding-right: 5px;padding-left: 5px;padding-top: 5px;padding-bottom: 5px;min-width: 100%;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <table class="divider_content" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px dotted #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <tbody>
                        <tr style="vertical-align: top">
                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                <span></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div class="">
	<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;"><![endif]-->
	<div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:120%; padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px;">
		<div style="font-size:12px;line-height:14px;color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;text-align:left;"><p style="margin: 0;font-size: 14px;line-height: 17px;text-align: center"><span style="font-size: 12px; line-height: 14px;">Copyright © SportWrench Inc. All rights reserved</span></p></div>
	</div>
	<!--[if mso]></td></tr></table><![endif]-->
</div>

              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
    <div style="background-color:transparent;">
      <div style="Margin: 0 auto;min-width: 320px;max-width: 640px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #FFFFFF;" class="block-grid ">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
          <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]-->

              <!--[if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]-->
            <div class="col num12" style="min-width: 320px;max-width: 640px;display: table-cell;vertical-align: top;">
              <div style="background-color: transparent; width: 100% !important;">
              <!--[if (!mso)&(!IE)]><!--><div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;"><!--<![endif]-->


                    <div align="center" class="img-container center  autowidth  " style="padding-right: 0px;  padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px;line-height:0px;"><td style="padding-right: 0px; padding-left: 0px;" align="center"><![endif]-->
  <a href="https://sportwrench.com" target="_blank">
    <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;width: 100%;max-width: 84px" width="84" />
  </a>
<!--[if mso]></td></tr></table><![endif]-->
</div>


              <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
              </div>
            </div>
          <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
      </div>
    </div>
   <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
		</td>
  </tr>
  </tbody>
  </table>
  <!--[if (mso)|(IE)]></div><![endif]-->


</body></html>', '{event_name} Withdrawal Received!', 'BF-basic-newsletter You Have Withdrawn from {event_name} We have received your withdrawal notice from {event_name}. Thank you for your initial interest and hopefully we can welcome you as part of our  staff at another time. Thank you, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ', null, 'Staff Withdrew (new)', '{"page": {"body": {"type": "mailup-bee-page-properties", "content": {"style": {"color": "#000000", "font-family": "Tahoma, Verdana, Segoe, sans-serif"}, "computedStyle": {"linkColor": "#0000FF", "messageWidth": "640px", "messageBackgroundColor": "#FFFFFF"}}, "webFonts": [], "container": {"style": {"background-color": "#F7F7F7"}}}, "rows": [{"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 18px; line-height: 21px;\"><strong><span style=\"line-height: 21px; font-size: 18px;\">You Have Withdrawn from {event_name}</span></strong></span></p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "divider": {"style": {"width": "100%", "border-top": "1px solid #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px;\"><span style=\"font-size: 14px; line-height: 16px;\">We have received your withdrawal notice from {event_name}. Thank you for your initial interest and hopefully we can welcome you as part of our&nbsp;</span><span style=\"font-size: 14px; line-height: 16px;\">staff at another time.</span></p>\n</div>", "style": {"color": "#555555", "font-family": "Tahoma, Verdana, Segoe, sans-serif", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}, {"type": "mailup-bee-newsletter-modules-text", "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 14px; line-height: 16px;\">Thank you,<br />{event_name} Staff</p>\n</div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "10px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "10px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-divider", "locked": false, "descriptor": {"style": {"padding-top": "5px", "padding-left": "5px", "padding-right": "5px", "padding-bottom": "5px"}, "divider": {"style": {"width": "100%", "border-top": "1px dotted #BBBBBB"}}, "computedStyle": {"align": "center"}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-text", "locked": false, "descriptor": {"text": {"html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>", "style": {"color": "#555555", "font-family": "inherit", "line-height": "120%"}, "computedStyle": {"linkColor": "#0000FF"}}, "style": {"padding-top": "0px", "padding-left": "10px", "padding-right": "10px", "padding-bottom": "0px"}, "computedStyle": {"hideContentOnMobile": false}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}, {"type": "one-column-empty", "locked": false, "columns": [{"style": {"border-top": "0px solid transparent", "border-left": "0px solid transparent", "padding-top": "5px", "border-right": "0px solid transparent", "padding-left": "0px", "border-bottom": "0px solid transparent", "padding-right": "0px", "padding-bottom": "5px", "background-color": "transparent"}, "modules": [{"type": "mailup-bee-newsletter-modules-image", "locked": false, "descriptor": {"image": {"alt": "Logo", "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "href": "https://sportwrench.com"}, "style": {"width": "100%", "padding-top": "0px", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "0px"}, "computedStyle": {"class": "center  autowidth ", "width": 84}}}], "grid-columns": 12}], "content": {"style": {"color": "#000000", "width": "640px", "background-color": "#FFFFFF", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}, "computedStyle": {"rowColStackOnMobile": true}}, "container": {"style": {"background-color": "transparent", "background-image": "none", "background-repeat": "no-repeat", "background-position": "top left"}}}], "title": "BF-basic-newsletter", "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "description": "BF-basic-newsletter"}}', 'staff.withdrew', true, 'staff', true);
---------------------------------------------------------------------------------------------------------------


-- SET default email template ID for "staff" email template types ---------------------------------------------
UPDATE email_template_type ett
SET default_email_template_id = (SELECT et."email_template_id"
                                 FROM email_template "et"
                                 WHERE et.email_template_type = ett.type)
WHERE ett.type IN ('staff.accepted', 'staff.applied', 'staff.declined', 'staff.waitlisted', 'staff.withdrew');
---------------------------------------------------------------------------------------------------------------


-- CREATE default event rows for staff trigger notifications --------------------------------------------------
INSERT INTO event_email_trigger ("email_template_type", "email_template_group", "email_template_id", "event_id")
SELECT et.email_template_type, et.email_template_group, et.email_template_id, 0
FROM email_template "et"
WHERE et.email_template_type IN
      ('staff.accepted', 'staff.applied', 'staff.declined', 'staff.waitlisted', 'staff.withdrew');
---------------------------------------------------------------------------------------------------------------


COMMIT;
