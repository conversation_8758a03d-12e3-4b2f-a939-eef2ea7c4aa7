BEGIN;

-- CREATE FIELD "event_owner_request_granter_id" ---------------
ALTER TABLE "public"."user" ADD COLUMN "event_owner_request_granter_id" Integer;COMMENT ON COLUMN "public"."user"."event_owner_request_granter_id" IS '"user_id" of person who have granted the event owner role request.';
-- -------------------------------------------------------------

COMMIT;

CREATE OR REPLACE FUNCTION public.sum_matches_result()
RETURNS TRIGGER AS $$
DECLARE
update_sql text;
select_sql text;
sum_m_won integer;
sum_m_lost integer;
sum_s_won integer;
sum_s_lost integer;
sum_p_won integer;
sum_p_lost integer;
BEGIN
RAISE NOTICE '%, TG_ARGV = %;', TG_RELNAME, array_to_string(TG_ARGV, ', ');
select_sql :=
'SELECT 
     SUM(
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'')
            THEN (
                CAST( 
                    CASE 
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''matches_won'' 
                        ELSE m.results->''team2''->>''matches_won'' 
                    END 
                AS INTEGER)
            )
            ELSE 0
        END
     ),
     SUM(  
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'')
            THEN (
                 CAST( 
                     CASE
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''matches_lost'' 
                        ELSE m.results->''team2''->>''matches_lost'' 
                     END
                 AS INTEGER)
            )
            ELSE 0
         END
     ),
     SUM(    
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'') 
            THEN (
                CAST( 
                    CASE 
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''sets_won'' 
                        ELSE m.results->''team2''->>''sets_won'' 
                    END
                AS INTEGER)
            )
            ELSE 0
         END
     ),
     SUM(    
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'') 
            THEN (
                CAST( 
                    CASE 
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''sets_lost'' 
                        ELSE m.results->''team2''->>''sets_lost'' 
                    END
                AS INTEGER)
            )
            ELSE 0
         END
     ),
     SUM(    
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'') 
            THEN (
                CAST( 
                    CASE 
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''points_won'' 
                        ELSE m.results->''team2''->>''points_won'' 
                    END
                AS INTEGER)
            )
            ELSE 0
         END
     ),
     SUM(    
         CASE 
            WHEN (m.results IS NOT NULL AND m.results <> ''{}'') 
            THEN (
                 CAST( 
                     CASE 
                        WHEN m.team1_roster_id = $1 
                        THEN m.results->''team1''->>''points_lost'' 
                        ELSE m.results->''team2''->>''points_lost'' 
                     END
                 AS INTEGER)
            )
            ELSE 0
         END
     )
 FROM matches m 
 WHERE (m.team1_roster_id = $1 OR m.team2_roster_id = $1)
    AND m.event_id = $2';

update_sql := 
    'UPDATE roster_team SET ' || 
    ' matches_won = $1, ' || 
    ' matches_lost = $2, ' || 
    ' sets_won = $3, ' || 
    ' sets_lost = $4, ' || 
    ' points_won = $5, ' || 
    ' points_lost = $6 ' || 
    ' WHERE roster_team_id = $7';

    
    -- 1 --
    EXECUTE select_sql 
    INTO sum_m_won , sum_m_lost,sum_s_won,sum_s_lost,sum_p_won,sum_p_lost
    USING NEW."team1_roster_id", NEW.event_id;
    
    --RAISE NOTICE '%,%,%,%,%,%', sum_m_won, sum_m_lost, sum_s_won, sum_s_lost, sum_p_won, sum_p_lost;

    EXECUTE update_sql USING sum_m_won , sum_m_lost,sum_s_won, sum_s_lost, sum_p_won,sum_p_lost, NEW."team1_roster_id";
    
    -- 2 --
    EXECUTE select_sql 
    INTO sum_m_won , sum_m_lost,sum_s_won,sum_s_lost,sum_p_won,sum_p_lost
    USING NEW."team2_roster_id", NEW.event_id;
    
    --RAISE NOTICE '%,%,%,%,%,%', sum_m_won, sum_m_lost, sum_s_won, sum_s_lost, sum_p_won, sum_p_lost;

    EXECUTE update_sql USING sum_m_won , sum_m_lost,sum_s_won, sum_s_lost, sum_p_won,sum_p_lost, NEW."team2_roster_id";
    
    RETURN NEW;
END
$$
LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS sum_matches_result_trigger ON matches;

CREATE TRIGGER sum_matches_result_trigger
AFTER INSERT OR UPDATE
ON matches FOR EACH ROW
EXECUTE PROCEDURE sum_matches_result();
