exports.up = function (knex) {
    return knex.schema.raw(String.raw`
      -- Create teams.any.pending template ------------------------------------------------------------
      WITH main_template AS (
          INSERT INTO public.email_template
          (email_html, email_subject, email_text,
          event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
          img_name, email_template_type, is_valid, email_template_group, published,
          deleted)
          VALUES
          ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css$2family=Lato" 
          rel="stylesheet" type="text/css"><!--<![endif]--><style>
          *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:640px){.row-content{width:100%!important}.mobile_hide{display:none}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
          </style></head><body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
          class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:20px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><img src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg" style="display:block;height:auto;border:0;width:116px;max-width:100%" width="116" alt="Image" title="Image"></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
          </table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:0;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:18px;">{event_name}</span></p><p 
          style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px"><span style="font-size:14px;">Dear, {cd_first} {cd_last}</span></p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px">
          <span style="font-size:14px;"> Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}&nbsp; by SportWrench Inc.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><img src="https://sw-email-media.s3.amazonaws.com/images/sw-eo-42/editor_images/38df252d-8811-4cc1-afc7-3f9059594172.png" style="display:block;height:auto;border:0;width:100px;max-width:100%" width="100" alt="Image" title="Image"></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table 
          class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="33.333333333333336%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Amount paid:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td 
          class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{payment_amount}</span></p></div></div></td></tr></table></td><td class="column column-2" 
          width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif">
          <div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Date</span> paid:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{payment_date_paid}</span></p>
          </div></div></td></tr></table></td><td class="column column-3" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
          style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Payment method:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
          <span style="font-size:16px;">&nbsp;{payment_type}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" 
          width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:10px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
          style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;mso-line-height-alt:18px;color:#000;line-height:1.5;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;mso-line-height-alt:18px">&nbsp;</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" 
          role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">PURCHASE SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
          width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">
          <strong><span style="font-size:14px;">Event</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
          style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{event_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table 
          class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Team Name<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{team_names_flat} </span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
          width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Purchase status<br></span></strong></p></div>
          </div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_status}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
          width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">
          <strong><span style="font-size:14px;">Total amount<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td 
          class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_amount}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table>
          <table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:10px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-14" align="center" width="100%" 
          border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;">If you have any questions, please contact event manager for details:</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-15" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" 
          role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
          class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p 
          style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Name</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{eo_first} {eo_last}</span></p></div>
          </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-16" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Email</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
          <table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p 
          style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_email}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-17" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
          style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Phone</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class 
          style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_phone}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-18" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
          class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" 
          cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table>
          </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-19" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
          style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class 
          style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{facebook_icon}</p></div></div></td></tr></table></td><td class="column column-2" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
          class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p 
          style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{twitter_icon}</p></div></div></td></tr></table></td><td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{instagram_icon}</p></div></div></td></tr></table></td><td 
          class="column column-4" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div 
          style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{snapchat_icon}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-20" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
          class="divider_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" 
          style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-21" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
          style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:0;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
          <tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2">Copyright © 2022 SportWrench Inc., All rights reserved.<br>You subscribed to our newsletter via our website, <a style="text-decoration: underline; color: #71777D;" href="sportwrench.com" target="_blank" rel="noopener">sportwrench.com</a></div></div></td></tr></table>
          </td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>', 
          'ACH purchase with pending payment {event_name}', 
          '{event_name} Dear, {cd_first} {cd_last}
          Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}  by SportWrench Inc.Amount paid:{payment_amount}
          Date paid:{payment_date_paid}
          Payment method:
          ACH PURCHASE SUMMARY
          Event{event_name}Team Name{team_names} Purchase status
          {payment_status}
          Total amount{payment_amount}
          If you have any questions, please contact event manager for details:Name{eo_first} {eo_last}
          Email
          {eo_email}Phone{eo_phone}
          {facebook_icon}{twitter_icon}{instagram_icon}{snapchat_icon}
          Copyright © 2022 SportWrench Inc., All rights reserved.You subscribed to our newsletter via our website, sportwrench.com', null, null, null, 'ACH pending payment', null,
          '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "Lato, Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#71777D",
                    "messageWidth": "620px",
                    "messageBackgroundColor": "transparent"
                  }
                },
                "webFonts": [
                  {
                    "url": "https://fonts.googleapis.com/css$2family=Lato",
                    "name": "Lato",
                    "fontFamily": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                  }
                ],
                "container": {
                  "style": {
                    "background-color": "#FFFFFF"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "uuid": "10b34a47-165f-4b37-b728-79d905dadfbe",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "70312a55-5228-4987-9add-1560509d77f4",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "20px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "f34ad233-c34c-4f2f-8428-986a581b9097",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg",
                              "href": "",
                              "width": "116px",
                              "height": "40px"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "116px",
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "c1b13f06-df1c-4d85-800a-8fc1e1fe8788",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "092c1640-712a-4be1-a810-ba1c7ab5fe4e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "25c021c9-9304-43e0-846b-6c7efc6af8c3",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #222222"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "546c8578-4c5c-4245-8605-85aaee5d8d46",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "5c4349a5-763d-40f3-a404-f2179854bc26",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f3e1c511-090d-439f-a581-4ae8b8fa721f",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">{event_name}</span></p><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Dear, {cd_first} {cd_last}</span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"> Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}&nbsp; by SportWrench Inc.</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "054aecb0-af0b-4900-92e0-8b54f6332a7d",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "d2e748ce-91ac-4a81-ba54-e55992c7fca9",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "uuid": "ebd68e70-cdc5-4269-bc42-caa4b1e828a5",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Image",
                              "src": "https://sw-email-media.s3.amazonaws.com/images/sw-eo-42/editor_images/38df252d-8811-4cc1-afc7-3f9059594172.png",
                              "href": "",
                              "width": "100px",
                              "height": "100px"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": "100px"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "three-columns-empty",
                  "uuid": "570a8ddb-9a03-4d1d-888a-cad37e0cb46a",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "499da67c-5b08-4332-93f7-f5be08d0ac65",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8aeaa960-3528-40c1-ad30-9858e64dc8d7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Amount paid:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8e4ec367-73e4-48dd-b6c7-5a49a05481a2",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">{payment_amount}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "b3229b3e-1bd4-46a3-9f8c-8475282c6876",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "169ccbb4-3de4-471e-aa8b-4aa8a3fd4e86",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Date</span> paid:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8afd31b4-28ab-4a30-8159-a3b48db2cea7",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">{payment_date_paid}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    },
                    {
                      "uuid": "6add1d9d-74c1-45b8-9631-280b13077c8a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c185ae88-f985-44ad-a5c0-261f2e4e4462",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Payment method:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f3c67e28-8574-4e9a-af49-0362f2554736",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">&nbsp;{payment_type}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 4
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "1e0336b8-bd13-4e23-a06e-d6b13e45a0b6",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "a3a2f841-0284-42f0-ac50-06efc8b83e90",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "10px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "c25e2133-fa7d-4284-860f-27743f06eeb8",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 18px;\" data-mce-style=\"font-size: 12px; line-height: 18px;\"><p style=\"font-size: 12px; line-height: 18px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 18px; word-break: break-word;\">&nbsp;</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "150%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "733c09ea-11cd-488f-87f9-69784664b124",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "28d182b8-df8f-4d31-80b2-30a5a54e5754",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "#555555"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8ee293c9-937d-404e-aeb2-4b0f373dcb16",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; text-align: center; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">PURCHASE SUMMARY</span></p></div>",
                              "style": {
                                "color": "#FFFFFF",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "bce92630-ec9a-4f6f-a94b-d2e85c3b2bd2",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "37daee99-049a-48d8-9645-6aadd1e6c10a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "46a059fa-04d7-4ce7-8877-4e98db481f07",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Event</span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "813a8711-9ef8-47dc-9c90-b4d1d68fe8d7",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "a7866dab-eb90-404d-ab00-b0fbe3d403be",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{event_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "77e97640-6bb4-4aed-99c6-987d7b870f4a",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "87ccb30a-7167-498d-a04b-00b0d70bb501",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "e2806fac-2d78-462d-b4e3-947ce3a48834",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Team Name<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "b4db443f-73b7-480c-9456-8b78543c8287",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "82b81805-155c-4782-8228-3872b578363d",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{team_names_flat} </span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "9d731a0e-4335-412f-9853-4127b2d375e7",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "6b95cf21-f7c1-4682-9a19-e9ac4fbf385f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f92f5c8c-5c3b-439b-a0bc-b3396b77ea1d",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Purchase status<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "2c157195-129f-4055-a5b8-8899d63654da",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "0eae51b9-5ade-4e4f-a3d7-37cbd656f13f",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{payment_status}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "adefbf3f-6011-4b35-a836-468283b8768a",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "2606ba42-6c6e-4ed5-b1bf-3ba0509f5889",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "4dfab6a3-6005-4e06-a30f-6fa427456a09",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Total amount<br></span></strong></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "cbe9ef41-eb08-4798-b1cc-a87957e0eb12",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "37078b1e-de6d-4dbb-a4a9-5466d32ada55",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{payment_amount}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "edee5ff0-9dcd-4fbe-b830-e45c73712540",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "a6d56de4-2bce-4d0d-856b-8513cff80372",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "e7657463-3d32-4c59-aef1-f74f271ee9f1",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "4c0028bf-9e3e-4f50-9f6e-6fdc7a659985",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "d4362afb-f46f-45ce-900a-bc27ea424049",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "10px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "7972c3d8-208a-4a2f-81fc-db1c9cbbd8e0",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px solid #222222"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "9be201b1-1df8-427d-86da-2404d697f343",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "5e1d1d10-9d14-43db-ba20-f75da49e4412",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "89da6e13-63ab-4193-820c-6a02d689adfb",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"line-height: 16px; font-size: 14px;\" data-mce-style=\"line-height: 16px; font-size: 14px;\">If you have any questions, please contact event manager for details:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "227ba68b-89c3-44e8-9128-dd17b133f1be",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "6b36e19f-fa7d-4f3d-a06f-762d7b79b988",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "01319ac4-85aa-494b-96e6-35fecbe75966",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Name</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "51d97202-fa8a-42e8-a537-e0ac40cfb81f",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "803e0f59-e059-43e6-921f-4bd47056042b",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{eo_first} {eo_last}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "b323feb9-429c-427c-88e3-c70e2db31af2",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "0d08451c-329f-4052-8854-2b36dee6f317",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "d85a661c-365b-41e0-abf5-f4594fec4875",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Email</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "90debaca-276e-4b27-bbcc-ffb3a5a08eb2",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "89ba41ab-8f7b-4622-acce-d50da4c11e36",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">{eo_email}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "row-2-columns-3-9",
                  "uuid": "c10322c5-4450-4ac6-852b-87c5f866e3e5",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "f5112047-6e5d-4831-895b-45b22a04af4d",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "4b4efc88-33dd-404e-b7be-f115f4365044",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Phone</strong></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "87540b51-dbdd-4c01-8e91-e54b0bc662e9",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "ae1d0f0c-44e2-41e8-a0e2-840510a8951b",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">{eo_phone}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 9
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "dbd5adf1-23ee-49a7-b7a7-1116591d7b65",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "78859b63-d861-4574-be8e-b9c4e24021af",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "9aa34d38-d4ac-47ee-84e0-72b4a65aaac0",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "20px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "20px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "four-columns-empty",
                  "uuid": "eecc6352-73c4-4ec7-9a16-f0dbb265f7ef",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "6af3d714-5b66-4b8d-a4b1-7f54234df2d4",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "de3042b7-d16e-49c7-9dbb-288e77e63abf",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{facebook_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "93affa0e-de86-4e71-9d91-e452af6e892a",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "6b5647e8-9d4c-475d-a4bc-66b61684bb25",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{twitter_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "351db305-0f2f-4d3d-befd-d691ca6259c0",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "27504eb0-9367-4515-8c12-759912a1ee6a",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{instagram_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "fb05999f-b55a-47cf-b5c6-e541cc253c8e",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "abc5b529-8ccb-49fc-85d6-7970c9fc9d30",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{snapchat_icon}</p></div>",
                              "style": {
                                "color": "#000000",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#000000"
                              }
                            },
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#333",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "618f2894-fd12-4c05-92f8-880c00ea62ef",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "7b7d89b2-5b49-431d-bdef-ec3148aa0780",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "0px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "39877890-3ff8-4d66-82e0-4b0e01385178",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "20px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "20px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #CCCCCC"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "a9cdad86-f59c-463f-a0a2-617b45c555c3",
                  "locked": false,
                  "columns": [
                    {
                      "uuid": "0aed55f7-44ba-4bc9-8015-6f8efb2753aa",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "14c10c2b-6aba-44df-b0d6-3eab7e5cb1f8",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\">Copyright © 2022 SportWrench Inc., All rights reserved. <br>You subscribed to our newsletter via our website, <a style=\"text-decoration: underline;\" href=\"sportwrench.com\" target=\"_blank\" rel=\"noopener\">sportwrench.com</a></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#71777D"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "620px",
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": ""
            },
            "comments": {}
          }',null, 'teams.any.pending', true, 'teams.payments', true, null)
              RETURNING email_template_id
        ), 
        insert_type AS (
            -- Create teams.any.pending template type ------------------------------------------------------------
            INSERT INTO public.email_template_type
            (type, email_template_group, title, description, long_title, is_trigger,
                default_email_template_id)
                VALUES ('teams.any.pending', 'teams.payments', 'ACH payment for teams pending',
                '<em>ACH payment for teams pending</em>', 'ACH payment for teams pending', true,
                (SELECT email_template_id
                    FROM main_template))
        )
    
        -- Create event email trigger for teams.any.pending template type ------------------------------------------------------------
        INSERT
            INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
            VALUES ('teams.any.pending', 'teams.payments', (SELECT email_template_id FROM main_template), 0);
    `);
  };
  
  exports.down = function (knex) {
    return knex.schema.raw(`
        DELETE FROM email_template WHERE email_template_type = 'teams.any.pending';
        DELETE FROM event_email_trigger WHERE email_template_type = 'teams.any.pending';
        DELETE FROM email_template_type WHERE type = 'teams.any.pending';
    `);
  };
  