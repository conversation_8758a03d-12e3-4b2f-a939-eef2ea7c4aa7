
exports.up = function(knex) {
    const email_text = `{event_name} Payment for "{event_name}" is pending. You will get notified when payment settles.  Payment details: Payment method: {payment_method} Amount: \${payment_net_amount} Merchant Fee: \${payment_merchant_fee} Total: \${payment_total_amount}                                                                   Copyright © SportWrench Inc. {current_year}. All rights reserved`;
    const email_html = `<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"> <html xmlns="http://www.w3.org/1999/xhtml"xmlns:v="urn:schemas-microsoft-com:vml"xmlns:o="urn:schemas-microsoft-com:office:office"> <head> <!--[if gte mso 9]>
        <xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
        <![endif]--> <meta http-equiv="Content-Type"content="text/html; charset=UTF-8"> <meta name="viewport"content="width=device-width,initial-scale=1"> <meta name="x-apple-disable-message-reformatting"> <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible"content="IE=edge"><!--<![endif]--> <title></title> <style type="text/css">@media only screen and (min-width:520px){.u-row{width:500px!important}.u-row .u-col{vertical-align:top}.u-row .u-col-100{width:500px!important}}@media (max-width:520px){.u-row-container{max-width:100%!important;padding-left:0!important;padding-right:0!important}.u-row .u-col{min-width:320px!important;max-width:100%!important;display:block!important}.u-row{width:100%!important}.u-col{width:100%!important}.u-col>div{margin:0 auto}}body{margin:0;padding:0}table,td,tr{vertical-align:top;border-collapse:collapse}p{margin:0}.ie-container table,.mso-container table{table-layout:fixed}*{line-height:inherit}a[x-apple-data-detectors=true]{color:inherit!important;text-decoration:none!important}table,td{color:#000}</style> </head> <body class="clean-body u_body"style="margin:0;padding:0;-webkit-text-size-adjust:100%;background-color:#f7f7f7;color:#000"> <!--[if IE]><div class="ie-container"><![endif]--> <!--[if mso]><div class="mso-container"><![endif]--> <table style="border-collapse:collapse;table-layout:fixed;border-spacing:0;mso-table-lspace:0;mso-table-rspace:0;vertical-align:top;min-width:320px;Margin:0 auto;background-color:#f7f7f7;width:100%"cellpadding="0"cellspacing="0"> <tbody> <tr style="vertical-align:top"> <td style="word-break:break-word;border-collapse:collapse!important;vertical-align:top"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0;font-family:arial,helvetica,sans-serif"align="left"> <div> <div style="text-align:left"> <p style="font-weight:700;font-size:18px;margin:18px 0"><b>{event_name}</b></p> <p style="margin:16px 0">Payment for "{event_name}" is pending.</p> <p style="margin:16px 0">You will get notified when payment settles.</p> <p> <span style="display:block"><b>Payment details:</b></span> <span style="margin-bottom:6px;display:block">Payment method: {payment_method}</span> <span style="margin-bottom:6px;display:block">Amount: \${payment_net_amount}</span> <span style="margin-bottom:6px;display:block">Merchant Fee: \${payment_merchant_fee}</span> <span style="margin-bottom:6px;display:block">Total: \${payment_total_amount}</span> </p> </div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:5px 5px 5px 5px;font-family:arial,helvetica,sans-serif"align="left"> <table height="0px"align="center"border="0"cellpadding="0"cellspacing="0"width="100%"style="border-collapse:collapse;table-layout:fixed;border-spacing:0;mso-table-lspace:0;mso-table-rspace:0;vertical-align:top;border-top:1px solid #bbb;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"> <tbody> <tr style="vertical-align:top"> <td style="word-break:break-word;border-collapse:collapse!important;vertical-align:top;font-size:0;line-height:0;mso-line-height-rule:exactly;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"> <span>&#160;</span> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0 10px 0 10px;font-family:arial,helvetica,sans-serif"align="left"> <div style="font-family:inherit;font-size:14px;color:#555;line-height:120%;text-align:left;word-wrap:break-word"> <div class="txtTinyMce-wrapper"style="font-size:12px;line-height:14px;font-family:inherit"data-mce-style="font-size:12px;line-height:14px;font-family:inherit;"><p style="font-size:14px;line-height:16px;text-align:center;word-break:break-word"data-mce-style="font-size:14px;line-height:16px;text-align:center;word-break:break-word;"><span style="font-size:12px;line-height:14px"data-mce-style="font-size:12px;line-height:14px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0;font-family:arial,helvetica,sans-serif"align="left"> <table width="100%"cellpadding="0"cellspacing="0"border="0"> <tr> <td style="padding-right:0;padding-left:0"align="center"> <img align="center"border="0"src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png"alt=""title=""style="outline:0;text-decoration:none;-ms-interpolation-mode:bicubic;clear:both;display:inline-block!important;border:none;height:auto;float:none;width:100%;max-width:72px"width="72"> </td> </tr> </table> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if (mso)|(IE)]></td></tr></table><![endif]--> </td> </tr> </tbody> </table> <!--[if mso]></div><![endif]--> <!--[if IE]></div><![endif]--> </body> </html>`;
    const unlayer_json = JSON.stringify({"body": {"id": "7F-SNcPeOL", "rows": [{"id": "CVr-SBG0eK", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_1", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "v5BXO0-7hd", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "a4ZCTJlpan", "type": "html", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "html": "<div style=\"text-align: left\">\n  <p style=\"font-weight: bold; font-size: 18px; margin: 18px 0;\"><b>{event_name}</b></p>\n  <p style=\"margin: 16px 0;\">Payment for \"{event_name}\" is pending.</p>\n  <p style=\"margin: 16px 0;\">You will get notified when payment settles.</p>\n  <p>\n  \t<span style=\"display: block\"><b>Payment details:</b></span>\n    <span style=\"margin-bottom: 6px; display: block\">Payment method: {payment_method}</span>\n    <span style=\"margin-bottom: 6px; display: block\">Amount: ${payment_net_amount}</span>\n    <span style=\"margin-bottom: 6px; display: block\">Merchant Fee: ${payment_merchant_fee}</span>\t <span style=\"margin-bottom: 6px; display: block\">Total: ${payment_total_amount}</span>\n   </p>\n</div>", "_meta": {"htmlID": "u_content_html_1", "htmlClassNames": "u_content_html"}, "anchor": "", "hideable": true, "deletable": true, "draggable": true, "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "0px 0px 0px 0px", "displayCondition": null}}]}]}, {"id": "ZR9v4R9cYn", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_2", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "WNqBeWfJ11", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "MAakmIzt3j", "type": "divider", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "_meta": {"htmlID": "u_content_divider_1", "htmlClassNames": "u_content_divider"}, "width": "100%", "anchor": "", "border": {"borderTopColor": "#BBBBBB", "borderTopStyle": "solid", "borderTopWidth": "1px"}, "hideable": true, "deletable": true, "draggable": true, "textAlign": "center", "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "5px 5px 5px 5px", "displayCondition": null}}]}]}, {"id": "jPgoiuTGlg", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "cL3AP2NOdr", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "XiJhIKAM-c", "type": "text", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "size": {"autoWidth": true}, "text": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:12px;line-height:14px;\" data-mce-style=\"font-size:12px;line-height:14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div>", "_meta": {"htmlID": "u_content_text_1", "htmlClassNames": "u_content_text"}, "color": "#555555", "anchor": "", "border": {}, "columns": false, "fontSize": "14px", "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textAlign": "left", "textColor": "#555555", "fontFamily": {"label": "Fonts", "value": "inherit"}, "lineHeight": "120%", "selectable": true, "headingType": "", "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "containerPadding": "0px 10px 0px 10px", "displayCondition": null}, "hasDeprecatedFontControls": true}]}]}, {"id": "C0g0V7M-Ml", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_4", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "H_ZaeN_7Zz", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "2_iYD6P9pw", "type": "image", "values": {"src": {"url": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "width": "72px", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "_meta": {"htmlID": "u_content_image_2", "htmlClassNames": "u_content_image"}, "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "anchor": "", "altText": "", "hideable": true, "deletable": true, "draggable": true, "textAlign": "center", "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "0px 0px 0px 0px", "displayCondition": null}}]}]}], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_body", "htmlClassNames": "u_body"}, "border": {}, "columns": false, "hideable": false, "language": {}, "deletable": true, "draggable": true, "linkStyle": {"body": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textColor": "#000000", "fontFamily": {"label": "Arial", "value": "arial,helvetica,sans-serif"}, "popupWidth": "600px", "selectable": true, "headingType": "", "hideDesktop": false, "popupHeight": "auto", "borderRadius": "10px", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "popupPosition": "center", "preheaderText": "", "backgroundColor": "#F7F7F7", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "contentVerticalAlign": "center", "popupBackgroundColor": "#FFFFFF", "popupBackgroundImage": {"url": "", "size": "cover", "repeat": "no-repeat", "position": "center", "fullWidth": true}, "popupCloseButton_action": {"name": "close_popup"}, "popupCloseButton_margin": "0px", "popupCloseButton_position": "top-right", "popupCloseButton_iconColor": "#000000", "popupOverlay_backgroundColor": "rgba(0, 0, 0, 0.1)", "popupCloseButton_borderRadius": "0px", "popupCloseButton_backgroundColor": "#DDDDDD"}, "footers": [], "headers": []}, "counters": {"u_row": 4, "u_column": 4, "u_content_html": 1, "u_content_menu": 0, "u_content_text": 1, "u_content_image": 2, "u_content_button": 0, "u_content_divider": 1, "u_content_heading": 0}, "schemaVersion": 16});

    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text = '${email_text}',
            email_html = '${email_html}',
            unlayer_json = '${unlayer_json}'
        WHERE email_template_id =
            (SELECT default_email_template_id FROM email_template_type WHERE type = 'teams_uncollected_fee_payments.pending');
    `);
};

exports.down = function(knex) {
    const email_text = `{event_name} Payment for "{event_name}" is pending. You will get notified when payment settles. Payment details:Payment method: {payment_method}Amount: \${payment_net_amount}Merchant Fee: \${payment_merchant_fee}Total: \${payment_total_amount}                                                                                                    Copyright © SportWrench Inc. {current_year}. All rights reserved`;
    const email_html = `<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"> <html xmlns="http://www.w3.org/1999/xhtml"xmlns:v="urn:schemas-microsoft-com:vml"xmlns:o="urn:schemas-microsoft-com:office:office"> <head> <!--[if gte mso 9]>
        <xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
        <![endif]--> <meta http-equiv="Content-Type"content="text/html; charset=UTF-8"> <meta name="viewport"content="width=device-width,initial-scale=1"> <meta name="x-apple-disable-message-reformatting"> <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible"content="IE=edge"><!--<![endif]--> <title></title> <style type="text/css">@media only screen and (min-width:520px){.u-row{width:500px!important}.u-row .u-col{vertical-align:top}.u-row .u-col-100{width:500px!important}}@media (max-width:520px){.u-row-container{max-width:100%!important;padding-left:0!important;padding-right:0!important}.u-row .u-col{min-width:320px!important;max-width:100%!important;display:block!important}.u-row{width:100%!important}.u-col{width:100%!important}.u-col>div{margin:0 auto}}body{margin:0;padding:0}table,td,tr{vertical-align:top;border-collapse:collapse}p{margin:0}.ie-container table,.mso-container table{table-layout:fixed}*{line-height:inherit}a[x-apple-data-detectors=true]{color:inherit!important;text-decoration:none!important}@media (max-width:480px){#u_content_image_1 .v-src-width{width:84px!important}#u_content_image_1 .v-src-max-width{max-width:100%!important}}</style> </head> <body class="clean-body u_body"style="margin:0;padding:0;-webkit-text-size-adjust:100%;background-color:#f7f7f7"> <!--[if IE]><div class="ie-container"><![endif]--> <!--[if mso]><div class="mso-container"><![endif]--> <table style="border-collapse:collapse;table-layout:fixed;border-spacing:0;mso-table-lspace:0;mso-table-rspace:0;vertical-align:top;min-width:320px;Margin:0 auto;background-color:#f7f7f7;width:100%"cellpadding="0"cellspacing="0"> <tbody> <tr style="vertical-align:top"> <td style="word-break:break-word;border-collapse:collapse!important;vertical-align:top"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: rgb(247, 247, 247);"><![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important;border-radius:0;-webkit-border-radius:0;-moz-border-radius:0"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent;border-radius:0;-webkit-border-radius:0;-moz-border-radius:0"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:arial,helvetica,sans-serif"align="left"> <div> <div style="text-align:left"> <p style="font-weight:700;font-size:18px"><b>{event_name}</b></p> <p>Payment for "{event_name}" is pending.</p> <p>You will get notified when payment settles.</p> <p><span style="margin-bottom:6px;display:block"><b>Payment details:</b><span><span style="margin-bottom:6px;display:block">Payment method: {payment_method}</span><span style="margin-bottom:6px;display:block">Amount: \${payment_net_amount}</span><span style="margin-bottom:6px;display:block">Merchant Fee: \${payment_merchant_fee}</span><span style="margin-bottom:6px;display:block">Total: \${payment_total_amount}</span></span></span></p> </div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <div class="u-row-container"style="background-color:#fff"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color: rgb(255, 255, 255);" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="background-color:#fff;height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;font-family:arial,helvetica,sans-serif"align="left"> <div style="word-wrap:break-word"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"><!-- [if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color: rgb(255, 255, 255);" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!-- [if (mso)|(IE)]><td align="center" width="500" style="background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="background-color:#fff;height:100%;width:100%!important"><!-- [if (!mso)&(!IE)]><!--> <div style="box-sizing:border-box;height:100%;padding:0;border:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"border="0"width="100%"cellspacing="0"cellpadding="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;font-family:arial,helvetica,sans-serif"align="left"> <div style="word-wrap:break-word"> <div style="border-collapse:collapse;display:table;width:100%"><!-- [if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color:transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width: 640px;"><tr class="layout-full-width" style="background-color:#FFFFFF;"><![endif]--> <!-- [if (mso)|(IE)]><td align="center" width="640" style=" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><![endif]--> <div class="col num12"style="min-width:320px;max-width:640px;width:calc(32000% - 204160px);background-color:#fff"> <div style="background-color:transparent;width:100%!important"><!-- [if (!mso)&(!IE)]><!--> <div style="border:0 solid transparent;padding:5px 0 5px 0"><!--<![endif]--> <div style="padding:5px"><!-- [if (mso)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px;padding-left: 5px; padding-top: 5px; padding-bottom: 5px;"><table width="100%" align="center" cellpadding="0" cellspacing="0" border="0"><tr><td><![endif]--> <div align="center"> <div class="unlayer2be"> <div class="unlayer2be"> <div style="border-top:1px dotted #bbb;width:100%;line-height:1px;height:1px;font-size:1px"> </div> </div> </div> </div> <!-- [if (mso)]></td></tr></table></td></tr></table><![endif]--></div> <!-- [if (!mso)&(!IE)]><!--></div> <!--<![endif]--></div> </div> <!-- [if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]--></div> </div> </td> </tr> </tbody> </table> <!-- [if (!mso)&(!IE)]><!--></div> <!--<![endif]--></div> </div> <!-- [if (mso)|(IE)]></td><![endif]--> <!-- [if (mso)|(IE)]></tr></table></td></tr></table><![endif]--></div> </div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <div class="u-row-container"style="background-color:#fff"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color: rgb(255, 255, 255);" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="background-color:#fff;height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;font-family:arial,helvetica,sans-serif"align="left"> <div style="word-wrap:break-word"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"><!-- [if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color: rgb(255, 255, 255);" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!-- [if (mso)|(IE)]><td align="center" width="500" style="background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="background-color:#fff;height:100%;width:100%!important"><!-- [if (!mso)&(!IE)]><!--> <div style="box-sizing:border-box;height:100%;padding:0;border:0 solid transparent;text-align:center"><span style="font-size:12px;line-height:14px">Copyright © SportWrench Inc. {current_year}. All rights reserved</span><br><!-- [if (!mso)&(!IE)]><!--></div> <!--<![endif]--></div> </div> <!-- [if (mso)|(IE)]></td><![endif]--> <!-- [if (mso)|(IE)]></tr></table></td></tr></table><![endif]--></div> </div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <div class="u-row-container"style="background-color:#fff"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:transparent"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="background-color: rgb(255, 255, 255);" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-color: transparent;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="background-color:#fff;height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table id="u_content_image_1"style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;font-family:arial,helvetica,sans-serif"align="left"> <table width="100%"cellpadding="0"cellspacing="0"border="0"> <tr> <td style="padding-right:0;padding-left:0"align="center"> <img align="center"border="0"src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png"alt=""title=""style="outline:0;text-decoration:none;-ms-interpolation-mode:bicubic;clear:both;display:inline-block!important;border:none;height:auto;float:none;width:100%;max-width:84px"width="84"class="v-src-width v-src-max-width"> </td> </tr> </table> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if (mso)|(IE)]></td></tr></table><![endif]--> </td> </tr> </tbody> </table> <!--[if mso]></div><![endif]--> <!--[if IE]></div><![endif]--> </body> </html>`;
    const unlayer_json = JSON.stringify({"body": {"id": "xdCaNdzWWv", "rows": [{"id": "nJwup5Flj7", "cells": [1], "values": {"_meta": {"htmlID": "u_row_5", "htmlClassNames": "u_row"}, "anchor": "", "columns": false, "padding": "0px", "hideable": true, "deletable": true, "draggable": true, "selectable": true, "hideDesktop": false, "duplicatable": true, "backgroundColor": "", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "center", "fullWidth": true}, "displayCondition": null, "columnsBackgroundColor": ""}, "columns": [{"id": "DgxJsV0pjv", "values": {"_meta": {"htmlID": "u_column_5", "htmlClassNames": "u_column"}, "border": {}, "padding": "0px", "borderRadius": "0px", "backgroundColor": ""}, "contents": [{"id": "pPbAp4Vbli", "type": "html", "values": {"html": "<div style=\"text-align: left\">\n  <p style=\"font-weight: bold; font-size: 18px;\"><b>{event_name}</b></p>\n  <p>Payment for \"{event_name}\" is pending.</p>\n  <p>You will get notified when payment settles.</p>\n  <p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b><span><span style=\"margin-bottom: 6px; display: block\">Payment method: {payment_method}</span><span style=\"margin-bottom: 6px; display: block\">Amount: ${payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Fee: ${payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total: ${payment_total_amount}</span></span></span></p>\n</div>", "_meta": {"htmlID": "u_content_html_2", "htmlClassNames": "u_content_html"}, "anchor": "", "hideable": true, "deletable": true, "draggable": true, "selectable": true, "hideDesktop": false, "duplicatable": true, "containerPadding": "10px", "displayCondition": null}}]}]}, {"id": "lba-iqsBUv", "cells": [12], "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_2", "htmlClassNames": "u_row"}, "color": "", "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": ""}, "columns": [{"id": "1gXSIRrEWf", "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "color": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": true, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "0t3mKPzEAL", "type": "text", "values": {"src": {"width": "auto", "height": "auto"}, "size": {"width": "320px", "autoWidth": true}, "text": "<div class=\"u-row\" style=\"margin: 0 auto; min-width: 320px; max-width: 500px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;\">\n<div style=\"border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;\"><!-- [if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"background-color: rgb(255, 255, 255);\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:500px;\"><tr style=\"background-color: transparent;\"><![endif]--> <!-- [if (mso)|(IE)]><td align=\"center\" width=\"500\" style=\"background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px; min-width: 500px; display: table-cell; vertical-align: top;\">\n<div style=\"background-color: #fff; height: 100%; width: 100%!important;\"><!-- [if (!mso)&(!IE)]><!-->\n<div style=\"box-sizing: border-box; height: 100%; padding: 0; border: 0 solid transparent;\"><!--<![endif]-->\n<table style=\"font-family: arial,helvetica,sans-serif;\" role=\"presentation\" border=\"0\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\">\n<tbody>\n<tr>\n<td style=\"overflow-wrap: break-word; word-break: break-word; font-family: arial,helvetica,sans-serif;\" align=\"left\">\n<div style=\"word-wrap: break-word;\">\n<div style=\"border-collapse: collapse; display: table; width: 100%;\"><!-- [if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"background-color:transparent;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width: 640px;\"><tr class=\"layout-full-width\" style=\"background-color:#FFFFFF;\"><![endif]--> <!-- [if (mso)|(IE)]><td align=\"center\" width=\"640\" style=\" width:640px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;\" valign=\"top\"><![endif]-->\n<div class=\"col num12\" style=\"min-width: 320px; max-width: 640px; width: calc(32000% - 204160px); background-color: #fff;\">\n<div style=\"background-color: transparent; width: 100%!important;\"><!-- [if (!mso)&(!IE)]><!-->\n<div style=\"border: 0 solid transparent; padding: 5px 0 5px 0;\"><!--<![endif]-->\n<div style=\"padding: 5px;\"><!-- [if (mso)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding-right: 5px;padding-left: 5px; padding-top: 5px; padding-bottom: 5px;\"><table width=\"100%\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td><![endif]-->\n<div align=\"center\">\n<div class=\"unlayer2be\">\n<div class=\"unlayer2be\">\n<div style=\"border-top: 1px dotted #bbb; width: 100%; line-height: 1px; height: 1px; font-size: 1px;\"> </div>\n</div>\n</div>\n</div>\n<!-- [if (mso)]></td></tr></table></td></tr></table><![endif]--></div>\n<!-- [if (!mso)&(!IE)]><!--></div>\n<!--<![endif]--></div>\n</div>\n<!-- [if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]--></div>\n</div>\n</td>\n</tr>\n</tbody>\n</table>\n<!-- [if (!mso)&(!IE)]><!--></div>\n<!--<![endif]--></div>\n</div>\n<!-- [if (mso)|(IE)]></td><![endif]--> <!-- [if (mso)|(IE)]></tr></table></td></tr></table><![endif]--></div>\n</div>", "_meta": {"htmlID": "u_content_text_1", "htmlClassNames": "u_content_text"}, "color": "", "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "containerPadding": "", "displayCondition": null}, "hasDeprecatedFontControls": true}]}]}, {"id": "FaRKdHMBPg", "cells": [12], "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row"}, "color": "", "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": ""}, "columns": [{"id": "voYXm6rbwI", "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "color": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": true, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "rZOFSUvaE-", "type": "text", "values": {"src": {"width": "auto", "height": "auto"}, "size": {"width": "320px", "autoWidth": true}, "text": "<div class=\"u-row\" style=\"margin: 0 auto; min-width: 320px; max-width: 500px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;\">\n<div style=\"border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;\"><!-- [if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"background-color: rgb(255, 255, 255);\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:500px;\"><tr style=\"background-color: transparent;\"><![endif]--> <!-- [if (mso)|(IE)]><td align=\"center\" width=\"500\" style=\"background-color: rgb(255, 255, 255);width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px; min-width: 500px; display: table-cell; vertical-align: top;\">\n<div style=\"background-color: #fff; height: 100%; width: 100%!important;\"><!-- [if (!mso)&(!IE)]><!-->\n<div style=\"box-sizing: border-box; height: 100%; padding: 0px; border: 0px solid transparent; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span><br /><!-- [if (!mso)&(!IE)]><!--></div>\n<!--<![endif]--></div>\n</div>\n<!-- [if (mso)|(IE)]></td><![endif]--> <!-- [if (mso)|(IE)]></tr></table></td></tr></table><![endif]--></div>\n</div>", "_meta": {"htmlID": "u_content_text_1", "htmlClassNames": "u_content_text"}, "color": "", "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "containerPadding": "", "displayCondition": null}, "hasDeprecatedFontControls": true}]}]}, {"id": "CjVs9n-mGw", "cells": [12], "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_4", "htmlClassNames": "u_row"}, "color": "", "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": ""}, "columns": [{"id": "Rr_tt27PEA", "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "color": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": true, "padding": "", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "textAlign": "", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "rgb(255, 255, 255)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "WCe7YMJ_7t", "type": "image", "values": {"src": {"url": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "width": 84, "height": 60}, "size": {"width": "", "autoWidth": true}, "text": "<div class=\"unlayer2be\"><img align=\"center\" border=\"0\" src=\"https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png\" alt=\"\" title=\"\" style=\"outline:0;text-decoration:none;-ms-interpolation-mode:bicubic;clear:both;display:inline-block!important;border:none;height:auto;float:none;width:100%;max-width:84px\" width=\"84\" class=\"v-src-width v-src-max-width\"></div>", "_meta": {"htmlID": "u_content_image_1", "htmlClassNames": "u_content_image"}, "color": "", "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "anchor": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "altText": "", "columns": false, "padding": "", "fontSize": "", "hideable": false, "_override": {"mobile": {"src": {"url": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "width": 84, "height": 60}}}, "deletable": true, "draggable": true, "textAlign": "center", "textColor": "", "fontFamily": {}, "lineHeight": "", "selectable": true, "headingType": "", "hideDesktop": false, "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "containerPadding": "", "displayCondition": null}}]}]}], "values": {"size": {"width": "", "autoWidth": true}, "text": "", "_meta": {"htmlID": "u_body", "htmlClassNames": "u_body"}, "color": "", "border": {"borderTop": "", "borderLeft": "", "borderRight": "", "borderBottom": ""}, "columns": false, "padding": "0px", "fontSize": "", "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"body": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textAlign": "", "textColor": "", "fontFamily": {"label": "Arial", "value": "arial,helvetica,sans-serif"}, "lineHeight": "", "popupWidth": "600px", "selectable": true, "headingType": "", "hideDesktop": false, "popupHeight": "auto", "borderRadius": "", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "popupPosition": "center", "preheaderText": "", "backgroundColor": "rgb(247, 247, 247)", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "contentVerticalAlign": "center", "popupBackgroundColor": "#FFFFFF", "popupBackgroundImage": {"url": "", "size": "cover", "repeat": "no-repeat", "position": "center", "fullWidth": true}, "popupCloseButton_action": {"name": "close_popup"}, "popupCloseButton_margin": "0px", "popupCloseButton_position": "top-right", "popupCloseButton_iconColor": "#000000", "popupOverlay_backgroundColor": "rgba(0, 0, 0, 0.1)", "popupCloseButton_borderRadius": "0px", "popupCloseButton_backgroundColor": "#DDDDDD"}, "footers": [], "headers": []}, "counters": {"u_row": 5, "u_column": 5, "u_content_html": 2, "u_content_menu": 0, "u_content_text": 3, "u_content_image": 1, "u_content_button": 0, "u_content_heading": 0}, "schemaVersion": 16});

    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text = '${email_text}',
            email_html = '${email_html}',
            unlayer_json = '${unlayer_json}'
        WHERE email_template_id =
            (SELECT default_email_template_id FROM email_template_type WHERE type = 'teams_uncollected_fee_payments.pending');
    `);
};
