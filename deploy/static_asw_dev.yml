- hosts: all
  vars:
    deploy_folder: /home/<USER>/sw-admin

  tasks:
  - name: Copy files  to the server
    synchronize:
      src: ../dist
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--exclude=.git"

  - name: Ensure nginx has access to its
    file: dest={{deploy_folder}} group=www-data recurse=yes

  - name: run hashing
    shell: docker run --rm -v /home/<USER>/home/<USER>"SW_DB={{SW_DB}}" static-hash 
    changed_when: True
